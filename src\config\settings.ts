import { readFileSync } from 'fs'
import path from 'path'
import <PERSON><PERSON><PERSON> from 'prettyjson'
import { fileURLToPath } from 'url'
import tessConfigLoader from '@tess-f/shared-config'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load the TESS shared config
const configPaths = process.env.CONFIG_PATHS !== undefined ? process.env.CONFIG_PATHS.split(',') : []
const tessConfig = tessConfigLoader.load(configPaths)
console.log('TESS Shared config loaded\t' + configPaths.join(', '))

export default {
  server: {
    logAllRequests: process.env.LOG_ALL_REQUESTS === 'true',
    timeout: parseInt(process.env.SERVER_TIMEOUT ?? '2400'),
    port: parseInt(process.env.SERVER_PORT ?? '8080'),
    basePath: process.env.SERVER_BASE_PATH ?? '/api/'
  },

  logger: {
    level: tessConfig.logger.level,
    name: process.env.LOG_NAME ?? 'evaluation-engine',
    useConsole: tessConfig.logger.useConsole,
    useElasticsearch: tessConfig.logger.useElasticsearch,
    noColor: tessConfig.logger.noColor,
    enableFilter: tessConfig.logger.filterEnabled,
    eventTypes: tessConfig.logger.filterFor,
    elasticsearch: {
      index: process.env.ELASTICSEARCH_INDEX ?? 'eval-engine',
      options: {
        node: tessConfig.elasticsearch.host,
        auth: tessConfig.elasticsearch.elasticPassword !== undefined
          ? { username: tessConfig.elasticsearch.elasticUsername, password: tessConfig.elasticsearch.elasticPassword }
          : undefined
      }
    }
  },

  redis: {
    url: tessConfig.redis.host,
    password: tessConfig.redis.password,
    checklistUsersDatabase: tessConfig.redis.databases.evalChecklistUsers
  },

  sessionAuthority: {
    redisUrl: tessConfig.redis.host,
    redisPassword: tessConfig.redis.password,
    redisSessionDatabase: tessConfig.redis.databases.sessions,
    redisSessonLookupDatabase: tessConfig.redis.databases.sessionLookup,
    checkIp: tessConfig.security.session.checkIp,
    
    jwtSecret: tessConfig.security.jwtSecret,
    cookieSecret: tessConfig.security.cookieSecret,
    cookieSecure: tessConfig.security.cookieSecure,
    cookieSigned: tessConfig.security.cookieSigned,
    bypass: process.env.SESSION_AUTHORITY_BYPASS === 'true',

    options: {
      hmacSha256Path: tessConfig.security.hmacSha256Path,
      jwtDefaultExpiresIn: tessConfig.security.session.expiresInMilli
    }
  },

  mssql: {
    forceEncrypted: tessConfig.microsoftSql.forceEncrypted,
    streamChunkSize: tessConfig.microsoftSql.streamChunkSize,
    connectionConfig: {
      user: tessConfig.microsoftSql.username,
      password: tessConfig.microsoftSql.password,
      server: tessConfig.microsoftSql.host,
      database: tessConfig.microsoftSql.database,
      port: tessConfig.microsoftSql.port,
      debug: tessConfig.microsoftSql.debug,
      requestTimeout: tessConfig.microsoftSql.requestTimeout,
      connectionTimeout: tessConfig.microsoftSql.connectionTimeout,
      options: {
        encrypt: tessConfig.microsoftSql.encrypt,
        trustedConnection: tessConfig.microsoftSql.trustedConnection,
        enabledArithAbort: true,
        trustServerCertificate: tessConfig.microsoftSql.trustServerCertificate
      },
      pool: {
        max: tessConfig.microsoftSql.maxPoolSize,
        min: tessConfig.microsoftSql.minPoolSize,
        idleTimeoutMillis: tessConfig.microsoftSql.idleTimeoutMillisPool
      }
    }
  },

  amqp: {
    rpc_timeout: tessConfig.rabbitmq.remoteProcedureCalls.timeoutMilli,
    queue: 'evaluation-engine', // TODO: read from config
    config: {
      protocol: tessConfig.rabbitmq.protocol,
      hostname: tessConfig.rabbitmq.host,
      port: tessConfig.rabbitmq.port,
      heartbeat: tessConfig.rabbitmq.heartbeat,
      reconnectDelayMilli: tessConfig.rabbitmq.reconnectDelayMilli,
      reconnectMaxAttempts: tessConfig.rabbitmq.reconnectMaxAttempts,
      username: tessConfig.rabbitmq.username,
      password: tessConfig.rabbitmq.password
    },

    serviceQueues: {
      lrs: tessConfig.rabbitmq.remoteProcedureCalls.learningRecordStore.queue,
      system: tessConfig.rabbitmq.remoteProcedureCalls.systemConfig.queue
    }
  },

  print: function () {
    const _sanitizedCopy = JSON.parse(JSON.stringify(this))

    _sanitizedCopy.sessionAuthority.jwtSecret = '<hidden>'
    _sanitizedCopy.sessionAuthority.cookieSecret = '<hidden>'
    _sanitizedCopy.sessionAuthority.redisPassword = '<hidden>'
    _sanitizedCopy.mssql.connectionConfig.password = '<hidden>'
    _sanitizedCopy.redis.password = '<hidden>'
    if (this.logger.elasticsearch.options.auth !== undefined) {
      _sanitizedCopy.logger.elasticsearch.options.auth.password = '<hidden>'
    }

    const logo = readFileSync(path.join(__dirname, '/ascii-logo.txt')).toString()

    console.log('\n' + logo)
    console.log('-----------------------------')
    console.log(prettyjson.render(_sanitizedCopy, { noColor: this.logger.noColor }))
    console.log('-----------------------------')
  }
}
