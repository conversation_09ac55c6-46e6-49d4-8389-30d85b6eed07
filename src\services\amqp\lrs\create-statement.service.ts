import rabbitmq from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import { Statement } from '../../../models/internal/xapi/statement.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-AMQP.lrs.create-statement')

export default async function createStatement (statement: Statement): Promise<string> {
  let response: {
    success: boolean
    id?: string
    message?: string
  } | undefined

  try {
    response = await rabbitmq.executeRPC(
      settings.amqp.serviceQueues.lrs, {
        command: 'create',
        statement
      },
      settings.amqp.rpc_timeout
    )
  } catch (error) {
    log('error', 'Error executing RPC call', { errorMessage: getErrorMessage(error), success: false })
  }

  if (response?.success && response?.id !== undefined) {
    return response.id
  } else {
    throw new Error(response?.message ?? 'Unknown error')
  }
}
