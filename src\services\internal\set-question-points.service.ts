import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import { EvaluationQuestion, EvaluationQuestionTableName } from '@tess-f/sql-tables/dist/evaluations/evaluation-questions.js'
import { getErrorMessage } from '@tess-f/backend-utils'

/**
 * Determines and sets the points possible for a question, checking for overrides
 */
export default async function setQuestionPoints(
  question: QuestionWithOptions,
  evaluationId?: string,
  evaluationVersion?: number
): Promise<void> {
  if (evaluationId && evaluationVersion && question.Id && question.Version) {
    try {
      const override = await getRows<EvaluationQuestion>(EvaluationQuestionTableName, mssql.getPool().request(), {
        EvaluationId: evaluationId,
        EvaluationVersion: evaluationVersion,
        QuestionId: question.Id,
        QuestionVersion: question.Version
      })
      question.Points = override[0].QuestionPointsOverride ?? question.Points
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      if (errorMessage !== DB_Errors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }
  }
}