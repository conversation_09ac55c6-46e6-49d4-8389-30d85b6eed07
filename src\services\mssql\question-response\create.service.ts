import mssql, { addRow } from '@lcs/mssql-utility'
import { type QuestionResponse } from '@tess-f/sql-tables/dist/evaluations/question-response.js'
import QuestionResponseModel from '../../../models/question-response.model.js'

export default async function createQuestionResponse (questionResponse: QuestionResponseModel): Promise<QuestionResponseModel> {
  const record = await addRow<QuestionResponse>(mssql.getPool().request(), questionResponse)
  return new QuestionResponseModel(undefined, record)
}
