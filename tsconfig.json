{"compilerOptions": {"experimentalDecorators": true, "target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./build", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowJs": false, "typeRoots": ["src/types", "node_modules/@types"]}, "exclude": ["src/**/*.spec.ts", "eslint.config.ts"], "ts-node": {"files": true, "esm": true}}