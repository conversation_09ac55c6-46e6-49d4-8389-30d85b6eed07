import mssql, { addRow } from '@lcs/mssql-utility'
import { SessionQuestionModel } from '../../../models/session-question.model.js'
import { EvalSessionQuestion } from '@tess-f/sql-tables/dist/evaluations/session-questions.js'

export default async function createSessionQuestion (sessionQuestion: SessionQuestionModel): Promise<SessionQuestionModel> {
  const record = await addRow<EvalSessionQuestion>(mssql.getPool().request(), sessionQuestion)
  return new SessionQuestionModel(undefined, record)
}
