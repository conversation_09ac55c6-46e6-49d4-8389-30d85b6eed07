import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import createQuestionResponse from '../../mssql/question-response/create.service.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import saveQuestionScore from '../../mssql/session-question-scores/save-question-score.service.js'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'
import { EventType } from '@tess-f/shared-config'

const log = logger.create('Service-Internal.Grade-Ordering-Question')
/**
 * Grade ordering questions
 * User must arrange options in the correct order. Supports partial credit if enabled.
 * Uses OrderId field to determine correct sequence.
 * 
 * @param question The question with options
 * @param userResponses The user's responses (one per option with OrderId indicating position)
 * @returns Grading result with correctness and points earned
 */
export default async function gradeOrdering(
  question: QuestionWithOptions,
  userResponses: QuestionResponseModel[],
  sessionId: string
): Promise<SessionQuestionScoreModel> {
  // determine the number of correct responses the user has
  log('debug', 'Grading question', {  questionId: question.Id, sessionId, eventType: EventType.question_grade })
  const numberOfCorrectResponses = userResponses.reduce((previous, response) => {
    // is this response correct?
    // if we find a matching option on the question in this order
    // the option is correct
    const optionCorrect = question.Options.find(option => option.Id === response.fields.Id && option.OrderId === response.fields.OrderId)
    if (optionCorrect) {
      response.fields.Correct = true
      return previous + 1
    } else {
      response.fields.Correct = false
      return previous
    }
  }, 0)

  // save all the users responses
  await Promise.all(userResponses.map(async (response) => await createQuestionResponse(response)))
  
  const isFullyCorrect = numberOfCorrectResponses === question.Options.length
  const isPartiallyCorrect = numberOfCorrectResponses < question.Options.length && numberOfCorrectResponses > 0

  if (isFullyCorrect) {
    // user answered the question correct save the question score
    log('info', 'Successfully graded question, response is correct.', { questionId: question.Id, sessionId, success: true, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: question.Points!,
      Correct: true,
      PartiallyCorrect: false,
      Pending: false
    }))
  } else if (isPartiallyCorrect && question.EnablePartialCredit) {
    // user is partially correct
    log('info', 'Successfully graded question, response partially correct', { questionId: question.Id, sessionId, success: true, percentageCorrect: Math.round(numberOfCorrectResponses / question.Options.length), eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: Math.round((numberOfCorrectResponses / question.Options.length) * (question.Points ?? 1)),
      Correct: false,
      PartiallyCorrect: true,
      Pending: false
    }))
  } else {
    // the question is wrong
    log('info', 'Successfully graded question, response incorrect', { questionId: question.Id, sessionId, success: true, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Correct: false,
      PartiallyCorrect: false,
      Pending: false
    }))
  }
}
