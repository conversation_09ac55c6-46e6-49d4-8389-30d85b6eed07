import { Table } from '@lcs/mssql-utility'
import { type EvaluationSession, SessionFields, SessionsTableName } from '@tess-f/sql-tables/dist/evaluations/session.js'

export class SessionModel extends Table<EvaluationSession, EvaluationSession> {
  fields: EvaluationSession
  
  constructor (fields?: EvaluationSession, record?: EvaluationSession) {
    super(
      SessionsTableName,
      [
        SessionFields.EvalId,
        SessionFields.EvalVersion,
        SessionFields.UserId
      ]
    )

    this.fields = fields ?? {}
    if (record) this.importFromDatabase(record)
  }

  importFromDatabase (record: EvaluationSession): void {
    this.fields = {
      EvalId: record.EvalId,
      EvalVersion: record.EvalVersion,
      UserId: record.UserId,
      Id: record.Id,
      Start: record.Start ? new Date(record.Start) : undefined,
      End: record.End ? new Date(record.End) : undefined,
      SubmittedBy: record.SubmittedBy,
      Notes: record.Notes,
      Score: record.Score,
      Passed: record.Passed
    }
  }

  exportJsonToDatabase (): EvaluationSession {
    return {
      EvalId: this.fields.EvalId,
      EvalVersion: this.fields.EvalVersion,
      UserId: this.fields.UserId,
      Id: this.fields.Id,
      Start: this.fields.Start,
      End: this.fields.End,
      SubmittedBy: this.fields.SubmittedBy,
      Notes: this.fields.Notes,
      Score: this.fields.Score,
      Passed: this.fields.Passed
    }
  }
}
