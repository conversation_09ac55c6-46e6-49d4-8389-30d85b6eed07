import mssql from '@lcs/mssql-utility'
import { QuestionOptionFields, QuestionOptionsTableName } from '@tess-f/sql-tables/dist/evaluations/question-option.js'
import { type OptionVersion, OptionVersionViewName, OptionVersionFields } from '@tess-f/sql-tables/dist/evaluations/option-version-view.js'

export default async function getOptionsForQuestion (questionId: string, version: number): Promise<OptionVersion[]> {
  const request = mssql.getPool().request()
  request.input('questionId', questionId)
  request.input('questionVersion', version)

  const results = await request.query<OptionVersion>(`
    SELECT [${OptionVersionViewName}].*
    FROM [${OptionVersionViewName}]
      JOIN [${QuestionOptionsTableName}]
        ON [${QuestionOptionsTableName}].[${QuestionOptionFields.QuestionId}] = @questionId
        AND [${QuestionOptionsTableName}].[${QuestionOptionFields.QuestionVersion}] = @questionVersion
        AND [${QuestionOptionsTableName}].[${QuestionOptionFields.OptionId}] = [${OptionVersionViewName}].[${OptionVersionFields.Id}]
        AND [${QuestionOptionsTableName}].[${QuestionOptionFields.OptionVersion}] = [${OptionVersionViewName}].[${OptionVersionFields.Version}]
  `)

  return results.recordset
}
