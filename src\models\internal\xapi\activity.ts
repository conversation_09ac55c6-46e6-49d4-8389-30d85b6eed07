interface InteractionComponent {
  id: string
  description?: Record<string, string>
}

export type Activity = {
  id: string
  objectType?: string
  definition?: ActivityDefinition
}

type ActivityDefinition = {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'choice'
  choices?: InteractionComponent[]
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'true-false'
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'fill-in'
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'long-fill-in'
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'matching'
  source?: InteractionComponent[]
  target?: InteractionComponent[]
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'performance'
  steps?: InteractionComponent[]
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'sequencing'
  choices?: InteractionComponent[]
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'likert'
  scale?: InteractionComponent[]
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'numeric'
  correctResponsesPattern?: string[]
} | {
  name?: Record<string, string>
  description?: Record<string, string>
  type?: string
  moreInfo?: string
  extensions?: Record<string, unknown>
  interactionType: 'other'
  correctResponsesPattern?: string[]
}
