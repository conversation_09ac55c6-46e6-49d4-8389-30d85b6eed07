import express from 'express'
import cookieParser from 'cookie-parser'
import settings from '../config/settings.js'
import { metricsMiddleware } from '@tess-f/backend-utils/metrics'

// import routers
import checklistRouter from '../controllers/http/checklist/checklist.router.js'
import evaluationRouter from '../controllers/http/evaluations/evaluations.router.js'

// Create global router
const router = express.Router()

router.use(express.urlencoded({ extended: false, limit: '2mb' }))
router.use(express.json({ limit: '2mb' }))
router.use(cookieParser(settings.sessionAuthority.cookieSecret))
router.use(metricsMiddleware())

router.use('/checklist', checklistRouter)
router.use('/evaluation', evaluationRouter)

export default router
