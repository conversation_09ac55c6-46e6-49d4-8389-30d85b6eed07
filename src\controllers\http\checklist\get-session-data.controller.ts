import logger from '@lcs/logger'
import type { Request, Response } from 'express'
import { RedisClient } from '../../../services/redis/client.service.js'
import httpStatus from 'http-status'
import getEvaluation from '../../../services/mssql/evaluations/get.service.js'
import createEvaluationSession from '../../../services/mssql/session/create.service.js'
import { SessionModel } from '../../../models/session.model.js'
import { v4 as uuid } from 'uuid'
import { createHash } from 'crypto'
import createStatement from '../../../services/amqp/lrs/create-statement.service.js'
import { getSystemConfig } from '../../../services/amqp/system/get-system-config.service.js'
import getUser from '../../../services/mssql/user/get.service.js'
import { stripHtml } from 'string-strip-html'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z, ZodError } from 'zod'
import { EventType } from '@tess-f/shared-config/dist/tess-config/logger.js'

const { INTERNAL_SERVER_ERROR, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP.get-checklist-session-data', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {

    const id = z.string().regex(/([\dA-Fa-f])/).parse(req.params.id)

    const sessionData = await RedisClient.getChecklistUsers(id)
    log('info', 'Successfully retrieved data for session', { sessionId: id, evalId: sessionData.checklistId, success: true, userCount: sessionData.userIds.length, req, eventType: EventType.user_session_get })

    // we need to fetch the evaluation data so we can get the version of the eval for the sessions
    const evaluation = await getEvaluation(sessionData.checklistId)
    log('info', 'Successfully retrieved evaluation data', { evalId: sessionData.checklistId, success: true, req, eventType: EventType.evaluation_get })

    // for each user being assessed with this checklist we need to create a session
    // then we need to store those session id's in redis and return a global session id
    // to fetch the session user id map when the checklist is submitted
    const userSessionMap = new Map<string, string>()

    const systemConfig = await getSystemConfig()
    log('info', 'Successfully retrieved system config', { success: true, req, eventType: EventType.service_config })
    const submitter = await getUser(req.session.userId)
    log('info', 'Successfully retrieved active user profile data', { success: true, userId: req.session.userId, req, eventType: EventType.user_get })

    await Promise.all(
      sessionData.userIds.map(async (userId: string) => {
        const session = await createEvaluationSession(new SessionModel({
          UserId: userId,
          EvalId: sessionData.checklistId,
          EvalVersion: evaluation.Version ?? 1,
          Start: new Date()
        }))
  
        userSessionMap.set(userId, session.fields.Id ?? '')
        const user = await getUser(userId)
        log('info', 'Successfully retrieved user profile data', { success: true, userId, req, eventType: EventType.user_get })

        // submit an xAPI statement about the session
        try {
          await createStatement({
            actor: {
              objectType: 'Agent',
              name: `${user.FirstName} ${user.LastName}`,
              account: {
                homePage: systemConfig.Domain,
                name: user.Username ?? ''
              }
            },
            verb: {
              id: 'http://adlnet.gov/expapi/verbs/launched'
            },
            object: {
              id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/${sessionData.checklistId}`,
              objectType: 'Activity',
              definition: {
                description: {
                  'en-US': `${stripHtml(evaluation.Description ?? '').result}`
                },
                name: {
                  'en-US': `${stripHtml(evaluation.Title ?? '').result}`
                }
              }
            },
            context: {
              extensions: {
                "https://w3id.org/xapi/cmi5/context/extensions/sessionid": session.fields.Id
              },
              instructor: {
                objectType: 'Agent',
                name: `${submitter.FirstName} ${submitter.LastName}`,
                account: {
                  homePage: systemConfig.Domain,
                  name: submitter.Username ?? ''
                }
              }
            },
            authority: {
              objectType: 'Group',
              member: [
                {
                  objectType: 'Agent',
                  name: `${submitter.FirstName} ${submitter.LastName}`,
                  account: {
                    homePage: systemConfig.Domain,
                    name: submitter.Username ?? ''
                  }
                },
                {
                  objectType: 'Agent',
                  name: 'Evaluation Engine',
                  mbox: 'mailto:<EMAIL>'
                }
              ]
            }
          })
        } catch (error) {
          log('error', 'Failed to submit statement', { success: false, errorMessage: getErrorMessage(error), req })
        }
      })
    )

    // now that we have all the session ids we can store them in redis
    // create a unique id for the session
    const hasher = createHash('sha256')
    hasher.update(Buffer.from(uuid()).toString('base64'))
    const sessionId = hasher.digest('hex')

    // Store the user ids in redis
    await RedisClient.setUserSessionData(sessionId, userSessionMap)

    res.json({
      ...sessionData,
      sessionId
    })
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === 'Session invalid') {
      log('warn', 'Invalid session: no data found in redis', { success: false, sessionId: req.params.id, req, eventType: EventType.not_found_db })
      res.status(BAD_REQUEST).send('Session Invalid')
    } else if (error instanceof ZodError) {
      log('warn', 'Invalid request data', { errorMessage: zodErrorToMessage(error), success: false, eventType: EventType.input_validation_errors })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
      log('error', 'Failed to get session data for checklist', { sessionId: req.params.id, success: false, error, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
