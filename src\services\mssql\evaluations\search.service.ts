import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import type { EvaluationItemFilters } from '../../../models/internal/evaluation-item-filters.js'
import type { ItemSearchOptions } from '../../../models/internal/item-search.js'
import type { PaginatedItems } from '../../../models/internal/paginated-items.js'
import type { SortOptions } from '../../../models/internal/sort-options.js'
import type { Request } from 'mssql'
import { CurrentEvaluationViewFields, CurrentEvaluationViewName, type  CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'
import { isSortDirectionValid, isValidString } from '@tess-f/backend-utils/validators'
import { Statues } from '@tess-f/sql-tables/dist/evaluations/status.js'
import { EvaluationTagFields, EvaluationTagsTableName } from '@tess-f/sql-tables/dist/evaluations/evaluation-tag.js'
import { TagFields, TagsTableName } from '@tess-f/sql-tables/dist/evaluations/tag.js'
import { UserTableName, UserFields } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { EvaluationConnectionTableName, EvaluationConnectionFields } from '@tess-f/sql-tables/dist/evaluations/evaluation-connection.js'

export default async function searchEvaluationItems (
  limit: number = 10,
  offset: number = 0,
  systemId: string,
  searchOptions?: ItemSearchOptions,
  filters?: EvaluationItemFilters,
  sortOptions?: SortOptions
): Promise<PaginatedItems<CurrentEvaluationView & { Imported: boolean }>> {
  const request = mssql.getPool().request()
  request.input('offset', offset)
  request.input('limit', limit)
  request.input('systemId', systemId)

  const query = `
    SELECT [${CurrentEvaluationViewName}].*,
      [CreatedByUser].[${UserFields.FirstName}] + ' ' + [CreatedByUser].[${UserFields.LastName}] AS [CreatedByName],
      [ModifiedByUser].[${UserFields.FirstName}] + ' ' + [ModifiedByUser].[${UserFields.LastName}] AS [ModifiedByName],
      IIF (
        (EXISTS (SELECT * FROM [${EvaluationConnectionTableName}] WHERE [${EvaluationConnectionFields.SystemId}] = @systemId AND [${EvaluationConnectionFields.EvaluationId}] = [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.Id}])),
        CAST(1 AS BIT),
        CAST(0 AS BIT)
      ) AS [Imported],
      TotalRecords = COUNT(*) OVER()
    FROM [${CurrentEvaluationViewName}]
		LEFT JOIN [${UserTableName}] AS [CreatedByUser] ON [CreatedByUser].[${UserFields.ID}] = [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedById}]
		LEFT JOIN [${UserTableName}] AS [ModifiedByUser] ON ([${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.ModifiedById}] IS NOT NULL AND [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.ModifiedById}] = [ModifiedByUser].[${UserFields.ID}]) OR ([${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.ModifiedById}] IS NULL AND [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedById}] = [ModifiedByUser].[${UserFields.ID}])
    WHERE [${CurrentEvaluationViewFields.StatusId}] = ${Statues.Published}
    ${buildSearchQuery(request, searchOptions)}
    ${buildFilters(request, filters)}
    ${buildSort(sortOptions)}
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<CurrentEvaluationView & { TotalRecords: number, Imported: boolean }>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    items: results.recordset
  }
}

function buildSearchQuery (request: Request, searchOptions?: ItemSearchOptions): string {
  if (searchOptions?.value !== undefined && searchOptions.value.length > 0) {
    const ids = searchOptions.value.split(' ').filter(term => !isNaN(Number(term))).map(id => parseInt(id))
    const scope = searchOptions.scope !== 'exact' ? searchOptions.scope : 'any'

    let query = `
      AND (
        ${parseSearchTerms(request, searchOptions.value, [CurrentEvaluationViewName + '].[' + CurrentEvaluationViewFields.Title], scope)}
        OR [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.Id}] IN (
          SELECT [${EvaluationTagFields.EvaluationId}]
          FROM [${EvaluationTagsTableName}]
          WHERE [${EvaluationTagFields.TagId}] IN (
            SELECT [${TagFields.Id}]
            FROM [${TagsTableName}]
            WHERE ${parseSearchTerms(request, searchOptions.value, [TagFields.Name], 'any', 'TagSearch')}
          )
        )
    `

    if (ids.length > 0) {
      const idParams = ids.map((id, index) => {
        request.input(`indexId_${index}`, id)
        return `@indexId_${index}`
      })
      query += ` OR ([${CurrentEvaluationViewFields.IndexId}] IN (${idParams.join(', ')}))`
    }

    query += ')'

    return query
  }

  return ''
}

function buildFilters (request: Request, filters?: EvaluationItemFilters): string {
  if (filters !== undefined) {
    return `
      ${filterEvaluationTypes(request, filters.evaluationTypeIds)}
      ${filterCreators(request, filters.createdByIds)}
      ${filterModifiers(request, filters.modifiedByIds)}
      ${filterCreatedDate(request, filters.createdBefore, filters.createdAfter)}
      ${filterModifiedDate(request, filters.modifiedBefore, filters.modifiedAfter)}
    `
  }

  return ''
}

function filterEvaluationTypes (request: Request, evaluationTypes?: number[]): string {
  if (evaluationTypes !== undefined && evaluationTypes.length > 0) {
    return `
      AND (
        [${CurrentEvaluationViewFields.EvaluationTypeId}] IN (
          ${evaluationTypes.map((id, index) => {
            request.input(`evalType_${index}`, id)
            return `@evalType_${index}`
          }).join(', ')}
        )
      )
    `
  }

  return ''
}

function filterCreators (request: Request, createdByIds?: string[]): string {
  if (createdByIds !== undefined && createdByIds.length > 0) {
    return `
      AND (
        [${CurrentEvaluationViewFields.CreatedById}] IN (
          ${createdByIds.map((id, index) => {
            request.input(`creator_${index}`, id)
            return `@creator_${index}`
          }).join(', ')}
        )
      )
    `
  }

  return ''
}

function filterModifiers (request: Request, modifiedByIds?: string[]): string {
  if (modifiedByIds !== undefined && modifiedByIds.length > 0) {
    return `
      AND (
        [${CurrentEvaluationViewFields.CreatedById}] IN (
          ${modifiedByIds.map((id, index) => {
            request.input(`modifier_${index}`, id)
            return `@modifier_${index}`
          }).join(', ')}
        )
      )
    `
  }

  return ''
}

function filterCreatedDate (request: Request, createdBefore?: Date, createdAfter?: Date): string {
  if (createdBefore !== undefined && createdAfter !== undefined) {
    request.input('createdBefore', createdBefore)
    request.input('createdAfter', createdAfter)
    return ` AND [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedOn}] BETWEEN @createdAfter AND @createdBefore `
  } else if (createdBefore !== undefined) {
    request.input('createdBefore', createdBefore)
    return ` AND [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedOn}] <= @createdBefore `
  } else if (createdAfter !== undefined) {
    request.input('createdAfter', createdAfter)
    return ` AND [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedOn}] >= @createdAfter `
  }

  return ''
}

function filterModifiedDate (request: Request, modifiedBefore?: Date, modifiedAfter?: Date): string {
  if (modifiedBefore !== undefined && modifiedAfter !== undefined) {
    request.input('modifiedBefore', modifiedBefore)
    request.input('modifiedAfter', modifiedAfter)
    return ` AND COALESCE([${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.ModifiedOn}], [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedOn}]) BETWEEN @modifiedAfter AND @modifiedBefore `
  } else if (modifiedBefore !== undefined) {
    request.input('modifiedBefore', modifiedBefore)
    return ` AND COALESCE([${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.ModifiedOn}], [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedOn}]) <= @modifiedBefore `
  } else if (modifiedAfter !== undefined) {
    request.input('modifiedAfter', modifiedAfter)
    return ` AND COALESCE([${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.ModifiedOn}], [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedOn}]) >= @modifiedAfter `
  }

  return ''
}

function buildSort (sort?: SortOptions): string {
  const direction = sort?.sortDirection !== undefined && isSortDirectionValid(sort.sortDirection.toUpperCase()) ? sort.sortDirection.toUpperCase() : 'ASC'

  if (sort?.sortColumn !== undefined) {
    if (
      isValidString(sort.sortColumn, [
        CurrentEvaluationViewFields.IndexId,
        CurrentEvaluationViewFields.Title,
        CurrentEvaluationViewFields.StatusId,
        CurrentEvaluationViewFields.CreatedOn
      ])
    ) {
      return `ORDER BY [${CurrentEvaluationViewName}].[${sort.sortColumn}] ${direction}`
    }

    if (sort.sortColumn === CurrentEvaluationViewFields.ModifiedOn) {
      return `ORDER BY COALESCE([${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.ModifiedOn}], [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedOn}]) ${direction}`
    }

    if (sort.sortColumn === CurrentEvaluationViewFields.CreatedById) {
      return `ORDER BY [CreatedByName] ${direction}`
    }

    if (sort.sortColumn === CurrentEvaluationViewFields.ModifiedById) {
      return `ORDER BY [ModifiedByName] ${direction}`
    }
  }

  // default sort by created date
  return `ORDER BY [${CurrentEvaluationViewName}].[${CurrentEvaluationViewFields.CreatedOn}] DESC`
}
