import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP submit session controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./submit-session.controller', {
            '../../../services/mssql/user/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve({}))
            },
            '../../../services/amqp/system/get-system-config.service.js': {
                getSystemConfig: Sinon.stub().returns(Promise.resolve({domain: 'localhost'}))
            },
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    getUserSessionData: Sinon.stub().resolves({})
                }
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: '123'
            },
            body: [
                {
                    UserId: uuid(),
                    Passed: true,
                    Notes: 'Test',
                    Responses: [
                        {
                            QuestionId: uuid(),
                            QuestionVersion: 1,
                            Duration: 'PT1H',
                            Notes: 'Test',
                            OptionId: uuid(),
                            OptionVersion: 1
                        }
                    ]
                }
            ]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.NO_CONTENT)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./submit-session.controller', {
            '../../../services/mssql/user/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve({}))
            },
            '../../../services/amqp/system/get-system-config.service.js': {
                getSystemConfig: Sinon.stub().returns(Promise.resolve({domain: 'localhost'}))
            },
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    getUserSessionData: Sinon.stub().resolves({})
                }
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: false
            },
            body: [
                {
                    UserId: uuid(),
                    Passed: true,
                    Notes: 'Test',
                    Responses: [
                        {
                            QuestionId: uuid(),
                            QuestionVersion: 1,
                            Duration: 'PT1H',
                            Notes: 'Test',
                            OptionId: uuid(),
                            OptionVersion: 1
                        }
                    ]
                }
            ]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid data')
        expect(data).to.include('id')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./submit-session.controller', {
            '../../../services/mssql/user/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({}))
            },
            '../../../services/amqp/system/get-system-config.service.js': {
                getSystemConfig: Sinon.stub().rejects(Promise.resolve({domain: 'localhost'}))
            },
            '../../../services/redis/client.service.js': {
                RedisClient: {
                    getUserSessionData: Sinon.stub().rejects({})
                }
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: '123'
            },
            body: [
                {
                    UserId: uuid(),
                    Passed: true,
                    Notes: 'Test',
                    Responses: [
                        {
                            QuestionId: uuid(),
                            QuestionVersion: 1,
                            Duration: 'PT1H',
                            Notes: 'Test',
                            OptionId: uuid(),
                            OptionVersion: 1
                        }
                    ]
                }
            ]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})