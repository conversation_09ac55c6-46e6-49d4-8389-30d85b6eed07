{"name": "evaluations-engine", "version": "1.0.1", "description": "Engine for all things taking and exploring evaluations (not authoring, that is handled by the authoring server)", "main": "./src/index.ts", "exports": "./build/index.js", "type": "module", "scripts": {"start": "node build/index.js", "start:dev": "cross-env CONFIG_PATHS=./tess-config.yaml nodemon --inspect", "build": "rimraf ./build && tsc && npm run copy-files", "copy-files": "copyfiles -u 1 src/**/*.txt build/", "test": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./test-config.yaml ts-mocha src/**/*.spec.ts --exit --timeout 999999", "test:ci": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./ci-test-config.yaml ts-mocha src/**/*.spec.ts --exit --timeout 999999", "test:coverage": "c8 npm test", "test:ci:coverage": "c8 npm run test:ci", "test:controllers": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./test-config.yaml ts-mocha src/controllers/**/*.spec.ts --exit --timeout 999999", "test:controllers:coverage": "c8 npm run test:controllers", "test:services": "cross-env LOAD_DEV_ENV=true CONFIG_PATHS=./test-config.yaml ts-mocha src/services/**/*.spec.ts --exit", "test:services:coverage": "c8 npm run test:services", "lint": "eslint", "lint:fix": "eslint --fix"}, "repository": {"type": "git", "url": "https://github.northgrum.com/LCS-TESS/Eval-Engine.git"}, "keywords": ["Express", "TypeScript", "ES", "<PERSON><PERSON>", "API"], "author": "Northrop Grumman", "license": "Northrop Grumman", "dependencies": {"@lcs/logger": "^5.0.0", "@lcs/mssql-utility": "^3.0.3", "@lcs/rabbitmq": "^4.0.3", "@lcs/session-authority": "^4.0.0", "@tess-f/backend-utils": "^2.1.1", "@tess-f/evaluations": "^1.0.22", "@tess-f/shared-config": "^2.0.31", "@tess-f/sql-tables": "^2.3.27", "@tess-f/system-config": "^2.0.3", "cookie-parser": "^1.4.7", "express": "^5.1.0", "http-status": "^2.1.0", "prettyjson": "^1.2.5", "prom-client": "^15.1.3", "redis": "^5.9.0", "string-strip-html": "^13.5.0", "uuid": "^13.0.0", "zod": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.39.1", "@stylistic/eslint-plugin": "^5.6.0", "@types/chai": "^5.2.3", "@types/cookie-parser": "^1.4.10", "@types/express": "^5.0.5", "@types/mocha": "^10.0.10", "@types/mssql": "^9.1.8", "@types/node": "^22.14.1", "@types/prettyjson": "^0.0.33", "@types/sinon": "^17.0.4", "c8": "^10.1.3", "chai": "^6.2.0", "copyfiles": "^2.4.1", "cross-env": "^10.1.0", "eslint": "^9.39.1", "esmock": "^2.7.3", "globals": "^16.5.0", "jiti": "^2.6.1", "mocha": "^11.7.1", "mocha-junit-reporter": "^2.2.1", "mocha-multi": "^1.1.7", "node-mocks-http": "^1.17.2", "nodemon": "^3.1.10", "rimraf": "^6.1.0", "sinon": "^21.0.0", "ts-mocha": "^11.1.0", "ts-node": "^10.9.2", "typescript": "^5.9.3", "typescript-eslint": "^8.47.0"}, "overrides": {"body-parser": "^1.20.3", "jws": "^3.2.3"}, "mocha": {"reporter": "mocha-multi", "reporterOptions": "spec=-,mocha-junit-reporter=-"}, "c8": {"reporter": ["text", "lcov"], "all": true, "src": ["/src"], "include": ["src/**"], "exclude": ["src/**/*.spec.ts"]}}