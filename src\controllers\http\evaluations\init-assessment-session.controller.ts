import logger from '@lcs/logger'
import type { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z, ZodError } from 'zod'
import createEvaluationSession from '../../../services/mssql/session/create.service.js'
import { SessionModel } from '../../../models/session.model.js'
import getEvaluation from '../../../services/mssql/evaluations/get.service.js'
import getUser from '../../../services/mssql/user/get.service.js'
import createStatement from '../../../services/amqp/lrs/create-statement.service.js'
import { getSystemConfig } from '../../../services/amqp/system/get-system-config.service.js'
import { stripHtml } from 'string-strip-html'
import { EventType } from '@tess-f/shared-config/dist/tess-config/logger.js'

const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
const log = logger.create('Controller-HTTP.init-assessment-session', httpLogTransformer)

/**
 * Initialize or retrieve an assessment session for a user and evaluation.
 * Checks if a session already exists for the specified user/evaluation combination.
 * If it exists, returns the existing session.
 * If not, creates a new session and returns the new session.
 *
 * POST /evaluation/init/:id
 * Params: { id: evaluationId }
 * 
 * Response: { session: SessionModel }
 */

export default async function initAssessmentSession (req: Request, res: Response): Promise<void> {
  try {
    const { id: evaluationId } = z.object({ id: zodGUID }).parse(req.params)
    const userId = req.session.userId
    const evaluation = await getEvaluation(evaluationId)

    // Create a new session
    const session = await createEvaluationSession(new SessionModel({
    UserId: userId,
    EvalId: evaluationId,
    EvalVersion: evaluation.Version!,
    Start: new Date()
    }))

    const sessionId = session.fields.Id

    log('info', 'Successfully created assessment session', {
      sessionId,
      evaluationId,
      userId,
      success: true,
      req,
      eventType: EventType.user_session_create
    })

    // Get system config and user data for xAPI statements
    const systemConfig = await getSystemConfig()
    const user = await getUser(userId)

    log('info', 'Successfully retrieved user profile data', {
      success: true,
      userId,
      req,
      eventType: EventType.user_get
    })

    // Submit an xAPI statement about the session launch
    try {
      await createStatement({
        actor: {
          objectType: 'Agent',
          name: `${user.FirstName} ${user.LastName}`,
          account: {
            homePage: systemConfig.Domain,
            name: user.Username ?? ''
          }
        },
        verb: {
          id: 'http://adlnet.gov/expapi/verbs/launched'
        },
        object: {
          id: `${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/${evaluationId}`,
          objectType: 'Activity',
          definition: {
            description: {
              'en-US': `${stripHtml(evaluation.Description ?? '').result}`
            },
            name: {
              'en-US': `${stripHtml(evaluation.Title ?? '').result}`
            }
          }
        },
        context: {
          extensions: {
            "https://w3id.org/xapi/cmi5/context/extensions/sessionid": sessionId
          }
        },
        authority: {
          objectType: 'Agent',
          name: 'Evaluation Engine',
          mbox: 'mailto:<EMAIL>'
        }
      })
      log('info', 'Successfully submitted xAPI launch statement', {
        sessionId,
        success: true,
        req,
        eventType: EventType.session_authority_logs
      })
    } catch (error) {
      log('warn', 'Failed to submit xAPI launch statement', {
        error,
        sessionId,
        success: false,
        req
      })
      // Don't fail the request if xAPI submission fails
    }

    res.json({ sessionId })

  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Invalid request data', {
        errorMessage: zodErrorToMessage(error),
        success: false,
        evaluationId: req.params.id,
        req,
        eventType: EventType.input_validation_errors
      })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
      log('error', 'Failed to initialize assessment session', {
        error,
        success: false,
        evaluationId: req.params.id,
        userId: req.body?.userId,
        req
      })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
