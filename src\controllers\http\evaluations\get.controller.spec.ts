import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import { EvaluationSectionWithQuestions } from '@tess-f/evaluations/dist/common/evaluation-section.js'
import type { CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'

describe('HTTP get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())

    describe('Basic validation tests', () => {
        it('returns success if the request data is valid', async () => {
            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().returns(Promise.resolve({ Id: uuid(), EqualizeObjectives: false, EqualizeQuestions: false, IncludeAllQuestions: false, NumberOfQuestions: 0 }))
                },
                '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                    default: Sinon.stub().returns(Promise.resolve([]))
                }
            })

            const mocks = httpMocks.createMocks({
                session: {
                    userId: uuid()
                },
                params: {
                    id: uuid()
                }
            })
            await controller(mocks.req, mocks.res)
            expect(mocks.res.statusCode).equal(httpStatus.OK)
        })

        it('returns an error if the request data is invalid', async () => {
            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().returns(Promise.resolve({ Id: uuid(), EqualizeObjectives: false, EqualizeQuestions: false, IncludeAllQuestions: false, NumberOfQuestions: 0 }))
                },
                '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                    default: Sinon.stub().returns(Promise.resolve([]))
                }
            })

            const mocks = httpMocks.createMocks({
                session: {
                    userId: uuid()
                },
                params: {
                    id: false
                }
            })
            await controller(mocks.req, mocks.res)
            expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
            const data = mocks.res._getData()
            expect(data).to.include('Invalid data')
            expect(data).to.include('id')
        })

        it('returns an internal server error if the request is rejected', async () => {
            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().rejects(Promise.resolve({ Id: uuid(), EqualizeObjectives: false, EqualizeQuestions: false, IncludeAllQuestions: false, NumberOfQuestions: 0 }))
                },
                '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                    default: Sinon.stub().rejects(Promise.resolve([]))
                }
            })

            const mocks = httpMocks.createMocks({
                session: {
                    userId: uuid()
                },
                params: {
                    id: uuid()
                }
            })
            await controller(mocks.req, mocks.res)
            expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
        })
    })

    describe('Assessment generation (EvaluationTypeId = 1)', () => {
        const mockEvalId = uuid()
        const mockSessionId = uuid()
        const mockUserId = uuid()

        it('generates new assessment with sections when no existing session data', async () => {
            const mockEvaluation: Partial<CurrentEvaluationView> = {
                Id: mockEvalId,
                Version: 1,
                EvaluationTypeId: 1,
                RandomizeQuestions: false
            }

            const mockSections: EvaluationSectionWithQuestions[] = [
                {
                    SectionId: 'section-1',
                    Version: 1,
                    Title: 'Section 1',
                    DisplayIndex: 1,
                    Questions: [
                        {
                            Id: uuid(),
                            Version: 1,
                            Stem: 'Question 1',
                            QuestionTypeId: 1,
                            Options: [
                                { Id: uuid(), Version: 1, Text: 'Option 1', Correct: true },
                                { Id: uuid(), Version: 1, Text: 'Option 2', Correct: false }
                            ]
                        } as QuestionWithOptions
                    ]
                },
                {
                    SectionId: 'section-2',
                    Version: 1,
                    Title: 'Section 2',
                    DisplayIndex: 2,
                    Questions: [
                        {
                            Id: uuid(),
                            Version: 1,
                            Stem: 'Question 2',
                            QuestionTypeId: 1,
                            Options: [
                                { Id: uuid(), Version: 1, Text: 'Option A', Correct: false },
                                { Id: uuid(), Version: 1, Text: 'Option B', Correct: true }
                            ]
                        } as QuestionWithOptions
                    ]
                }
            ]

            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().resolves(mockEvaluation)
                },
                '../../../services/internal/assessment-generator-service.js': {
                    default: Sinon.stub().resolves(mockSections)
                },
                '../../../services/mssql/session/get.service.js': {
                    sessionHasAssessmentData: Sinon.stub().resolves(false)
                }
            })

            const mocks = httpMocks.createMocks({
                session: { userId: mockUserId },
                params: { id: mockEvalId, session: mockSessionId }
            })

            await controller(mocks.req, mocks.res)

            expect(mocks.res.statusCode).equal(httpStatus.OK)
            const response = mocks.res._getJSONData()
            expect(response).to.have.property('evaluation')
            expect(response).to.have.property('questions')
            expect(response.questions).to.be.an('array')
            expect(response.questions).to.have.length(2)
            expect(response.questions[0]).to.have.property('SectionId')
            expect(response.questions[0]).to.have.property('Questions')
            // Verify Correct field is sanitized
            expect(response.questions[0].Questions[0].Options[0]).to.not.have.property('Correct')
        })

        it('loads existing assessment from session', async () => {
            const mockEvaluation: Partial<CurrentEvaluationView> = {
                Id: mockEvalId,
                Version: 1,
                EvaluationTypeId: 1
            }

            const mockLoadedQuestions: QuestionWithOptions[] = [
                {
                    Id: uuid(),
                    Version: 1,
                    Stem: 'Loaded Question',
                    QuestionTypeId: 1,
                    Options: [
                        { Id: uuid(), Version: 1, Text: 'Option 1', Correct: true }
                    ]
                } as QuestionWithOptions
            ]

            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().resolves(mockEvaluation)
                },
                '../../../services/internal/assessment-loader-service.js': {
                    default: Sinon.stub().resolves(mockLoadedQuestions)
                },
                '../../../services/mssql/session/get.service.js': {
                    sessionHasAssessmentData: Sinon.stub().resolves(true)
                }
            })

            const mocks = httpMocks.createMocks({
                session: { userId: mockUserId },
                params: { id: mockEvalId, session: mockSessionId }
            })

            await controller(mocks.req, mocks.res)

            expect(mocks.res.statusCode).equal(httpStatus.OK)
            const response = mocks.res._getJSONData()
            expect(response.questions).to.be.an('array')
            expect(response.questions).to.have.length(1)
            expect(response.questions[0].Stem).to.equal('Loaded Question')
            // Verify Correct field is sanitized
            expect(response.questions[0].Options[0]).to.not.have.property('Correct')
        })

        it('returns bad request when session is missing for assessment', async () => {
            const mockEvaluation: Partial<CurrentEvaluationView> = {
                Id: mockEvalId,
                Version: 1,
                EvaluationTypeId: 1
            }

            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().resolves(mockEvaluation)
                }
            })

            const mocks = httpMocks.createMocks({
                session: { userId: mockUserId },
                params: { id: mockEvalId } // No session parameter
            })

            await controller(mocks.req, mocks.res)

            expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
            expect(mocks.res._getData()).to.include('Missing session')
        })

        it('sanitizes sub-question options in assessment', async () => {
            const mockEvaluation: Partial<CurrentEvaluationView> = {
                Id: mockEvalId,
                Version: 1,
                EvaluationTypeId: 1
            }

            const mockSections: EvaluationSectionWithQuestions[] = [
                {
                    SectionId: 'section-1',
                    Version: 1,
                    Title: 'Section 1',
                    DisplayIndex: 1,
                    Questions: [
                        {
                            Id: uuid(),
                            Version: 1,
                            Stem: 'Parent Question',
                            QuestionTypeId: 2,
                            Options: [],
                            SubQuestions: [
                                {
                                    Id: uuid(),
                                    Version: 1,
                                    Stem: 'Sub Question',
                                    QuestionTypeId: 1,
                                    Options: [
                                        { Id: uuid(), Version: 1, Text: 'Sub Option', Correct: true }
                                    ]
                                } as QuestionWithOptions
                            ]
                        } as QuestionWithOptions
                    ]
                }
            ]

            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().resolves(mockEvaluation)
                },
                '../../../services/internal/assessment-generator-service.js': {
                    default: Sinon.stub().resolves(mockSections)
                },
                '../../../services/mssql/session/get.service.js': {
                    sessionHasAssessmentData: Sinon.stub().resolves(false)
                }
            })

            const mocks = httpMocks.createMocks({
                session: { userId: mockUserId },
                params: { id: mockEvalId, session: mockSessionId }
            })

            await controller(mocks.req, mocks.res)

            expect(mocks.res.statusCode).equal(httpStatus.OK)
            const response = mocks.res._getJSONData()
            // Verify sub-question Correct field is sanitized
            expect(response.questions[0].Questions[0].SubQuestions[0].Options[0]).to.not.have.property('Correct')
        })
    })

    describe('Checklist generation (EvaluationTypeId != 1)', () => {
        const mockEvalId = uuid()
        const mockUserId = uuid()

        it('generates checklist with all questions', async () => {
            const mockEvaluation: Partial<CurrentEvaluationView> = {
                Id: mockEvalId,
                Version: 1,
                EvaluationTypeId: 2, // Checklist
                EqualizeObjectives: false,
                EqualizeQuestions: false,
                IncludeAllQuestions: true,
                NumberOfQuestions: 0,
                RandomizeQuestions: false
            }

            const mockAllQuestions: QuestionWithOptions[] = [
                {
                    Id: uuid(),
                    Version: 1,
                    Stem: 'Checklist Question 1',
                    QuestionTypeId: 1,
                    OrderId: 1,
                    Options: [
                        { Id: uuid(), Version: 1, Text: 'Yes', Correct: true },
                        { Id: uuid(), Version: 1, Text: 'No', Correct: false }
                    ]
                } as QuestionWithOptions,
                {
                    Id: uuid(),
                    Version: 1,
                    Stem: 'Checklist Question 2',
                    QuestionTypeId: 1,
                    OrderId: 2,
                    Options: [
                        { Id: uuid(), Version: 1, Text: 'Pass', Correct: true },
                        { Id: uuid(), Version: 1, Text: 'Fail', Correct: false }
                    ]
                } as QuestionWithOptions
            ]

            const pickQuestionsStub = Sinon.stub().returns(mockAllQuestions)

            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().resolves(mockEvaluation)
                },
                '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                    default: Sinon.stub().resolves(mockAllQuestions)
                },
                '../../../services/internal/question-picker-service.js': {
                    default: pickQuestionsStub
                }
            })

            const mocks = httpMocks.createMocks({
                session: { userId: mockUserId },
                params: { id: mockEvalId }
            })

            await controller(mocks.req, mocks.res)

            expect(mocks.res.statusCode).equal(httpStatus.OK)
            const response = mocks.res._getJSONData()
            expect(response.questions).to.be.an('array')
            expect(response.questions).to.have.length(2)
            expect(response.questions[0].Stem).to.equal('Checklist Question 1')
            expect(response.questions[1].Stem).to.equal('Checklist Question 2')
            // Verify Correct field is sanitized
            expect(response.questions[0].Options[0]).to.not.have.property('Correct')
            expect(response.questions[1].Options[0]).to.not.have.property('Correct')
        })

        it('sorts checklist questions by OrderId when RandomizeQuestions is false', async () => {
            const mockEvaluation: Partial<CurrentEvaluationView> = {
                Id: mockEvalId,
                Version: 1,
                EvaluationTypeId: 2,
                EqualizeObjectives: false,
                EqualizeQuestions: false,
                IncludeAllQuestions: true,
                NumberOfQuestions: 0,
                RandomizeQuestions: false
            }

            const mockAllQuestions: QuestionWithOptions[] = [
                {
                    Id: uuid(),
                    Version: 1,
                    Stem: 'Question C',
                    QuestionTypeId: 1,
                    OrderId: 3,
                    Options: []
                } as QuestionWithOptions,
                {
                    Id: uuid(),
                    Version: 1,
                    Stem: 'Question A',
                    QuestionTypeId: 1,
                    OrderId: 1,
                    Options: []
                } as QuestionWithOptions,
                {
                    Id: uuid(),
                    Version: 1,
                    Stem: 'Question B',
                    QuestionTypeId: 1,
                    OrderId: 2,
                    Options: []
                } as QuestionWithOptions
            ]

            const pickQuestionsStub = Sinon.stub().returns(mockAllQuestions)

            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().resolves(mockEvaluation)
                },
                '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                    default: Sinon.stub().resolves(mockAllQuestions)
                },
                '../../../services/internal/question-picker-service.js': {
                    default: pickQuestionsStub
                }
            })

            const mocks = httpMocks.createMocks({
                session: { userId: mockUserId },
                params: { id: mockEvalId }
            })

            await controller(mocks.req, mocks.res)

            expect(mocks.res.statusCode).equal(httpStatus.OK)
            const response = mocks.res._getJSONData()
            expect(response.questions).to.have.length(3)
            // Verify questions are sorted by OrderId
            expect(response.questions[0].Stem).to.equal('Question A')
            expect(response.questions[1].Stem).to.equal('Question B')
            expect(response.questions[2].Stem).to.equal('Question C')
        })

        it('does not sort checklist questions when RandomizeQuestions is true', async () => {
            const mockEvaluation: Partial<CurrentEvaluationView> = {
                Id: mockEvalId,
                Version: 1,
                EvaluationTypeId: 2,
                EqualizeObjectives: false,
                EqualizeQuestions: false,
                IncludeAllQuestions: true,
                NumberOfQuestions: 0,
                RandomizeQuestions: true
            }

            const mockAllQuestions: QuestionWithOptions[] = [
                {
                    Id: uuid(),
                    Version: 1,
                    Stem: 'Question C',
                    QuestionTypeId: 1,
                    OrderId: 3,
                    Options: []
                } as QuestionWithOptions,
                {
                    Id: uuid(),
                    Version: 1,
                    Stem: 'Question A',
                    QuestionTypeId: 1,
                    OrderId: 1,
                    Options: []
                } as QuestionWithOptions
            ]

            const pickQuestionsStub = Sinon.stub().returns(mockAllQuestions)

            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().resolves(mockEvaluation)
                },
                '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                    default: Sinon.stub().resolves(mockAllQuestions)
                },
                '../../../services/internal/question-picker-service.js': {
                    default: pickQuestionsStub
                }
            })

            const mocks = httpMocks.createMocks({
                session: { userId: mockUserId },
                params: { id: mockEvalId }
            })

            await controller(mocks.req, mocks.res)

            expect(mocks.res.statusCode).equal(httpStatus.OK)
            const response = mocks.res._getJSONData()
            expect(response.questions).to.have.length(2)
            // Verify questions are NOT sorted (remain in original order)
            expect(response.questions[0].Stem).to.equal('Question C')
            expect(response.questions[1].Stem).to.equal('Question A')
        })

        it('picks subset of questions when IncludeAllQuestions is false', async () => {
            const mockEvaluation: Partial<CurrentEvaluationView> = {
                Id: mockEvalId,
                Version: 1,
                EvaluationTypeId: 2,
                EqualizeObjectives: false,
                EqualizeQuestions: false,
                IncludeAllQuestions: false,
                NumberOfQuestions: 2,
                RandomizeQuestions: false
            }

            const mockAllQuestions: QuestionWithOptions[] = [
                { Id: uuid(), Version: 1, Stem: 'Q1', QuestionTypeId: 1, OrderId: 1, Options: [] } as QuestionWithOptions,
                { Id: uuid(), Version: 1, Stem: 'Q2', QuestionTypeId: 1, OrderId: 2, Options: [] } as QuestionWithOptions,
                { Id: uuid(), Version: 1, Stem: 'Q3', QuestionTypeId: 1, OrderId: 3, Options: [] } as QuestionWithOptions
            ]

            const pickedQuestions = [mockAllQuestions[0], mockAllQuestions[1]]
            const pickQuestionsStub = Sinon.stub().returns(pickedQuestions)

            const controller = await esmock('./get.controller', {
                '../../../services/mssql/evaluations/get.service.js': {
                    default: Sinon.stub().resolves(mockEvaluation)
                },
                '../../../services/mssql/questions/get-all-for-evaluation.service.js': {
                    default: Sinon.stub().resolves(mockAllQuestions)
                },
                '../../../services/internal/question-picker-service.js': {
                    default: pickQuestionsStub
                }
            })

            const mocks = httpMocks.createMocks({
                session: { userId: mockUserId },
                params: { id: mockEvalId }
            })

            await controller(mocks.req, mocks.res)

            expect(mocks.res.statusCode).equal(httpStatus.OK)
            const response = mocks.res._getJSONData()
            expect(response.questions).to.have.length(2)
            // Verify question picker was called with correct parameters
            expect(pickQuestionsStub.calledOnce).to.equal(true)
            const pickerArgs = pickQuestionsStub.firstCall.args
            expect(pickerArgs[1]).to.deep.equal({
                EqualizeObjectives: false,
                EqualizeQuestions: false,
                IncludeAllQuestions: false,
                NumberOfQuestions: 2
            })
        })
    })
})