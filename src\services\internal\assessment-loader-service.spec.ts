import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import { v4 as uuid } from 'uuid'
import { SessionQuestionModel } from '../../models/session-question.model.js'
import { SessionOptionModel } from '../../models/session-option.model.js'
import { type QuestionVersion } from '@tess-f/sql-tables/dist/evaluations/question-version-view.js'
import { type OptionVersion } from '@tess-f/sql-tables/dist/evaluations/option-version-view.js'

describe('Assessment Loader Service', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  const mockSessionId = uuid()
  const mockQuestionId1 = uuid()
  const mockQuestionId2 = uuid()
  const mockOptionId1 = uuid()
  const mockOptionId2 = uuid()

  const mockSectionId = uuid()

  const mockSessionQuestions = [
    new SessionQuestionModel({
      SessionId: mockSessionId,
      QuestionId: mockQuestionId1,
      QuestionVersion: 1,
      PresentationIndex: 0,
      SectionId: mockSectionId,
      SectionVersion: 1,
      SectionDisplayIndex: 1,
      DisplayIndexWithinSection: 1,
      SectionTitle: 'Test Section'
    }),
    new SessionQuestionModel({
      SessionId: mockSessionId,
      QuestionId: mockQuestionId2,
      QuestionVersion: 1,
      PresentationIndex: 1,
      SectionId: mockSectionId,
      SectionVersion: 1,
      SectionDisplayIndex: 1,
      DisplayIndexWithinSection: 2,
      SectionTitle: 'Test Section'
    })
  ]

  const mockSessionOptions = [
    new SessionOptionModel({
      SessionId: mockSessionId,
      QuestionId: mockQuestionId1,
      QuestionVersion: 1,
      OptionId: mockOptionId1,
      OptionVersion: 1,
      PresentationIndex: 0
    }),
    new SessionOptionModel({
      SessionId: mockSessionId,
      QuestionId: mockQuestionId2,
      QuestionVersion: 1,
      OptionId: mockOptionId2,
      OptionVersion: 1,
      PresentationIndex: 0
    })
  ]

  const mockQuestionData1: QuestionVersion = {
    Id: mockQuestionId1,
    Version: 1,
    Stem: 'Test Question 1',
    QuestionTypeId: 1
  } as QuestionVersion

  const mockQuestionData2: QuestionVersion = {
    Id: mockQuestionId2,
    Version: 1,
    Stem: 'Test Question 2',
    QuestionTypeId: 1
  } as QuestionVersion

  const mockOptionData1: OptionVersion = {
    Id: mockOptionId1,
    Version: 1,
    Text: 'Option 1'
  } as OptionVersion

  const mockOptionData2: OptionVersion = {
    Id: mockOptionId2,
    Version: 1,
    Text: 'Option 2'
  } as OptionVersion

  it('should load assessment with questions and options in correct order', async () => {
    const getSessionQuestionsStub = Sinon.stub().resolves(mockSessionQuestions)
    const getSessionOptionsForQuestionStub = Sinon.stub()
      .onFirstCall().resolves([mockSessionOptions[0]])
      .onSecondCall().resolves([mockSessionOptions[1]])
    const getChildrenForQuestionStub = Sinon.stub().resolves([])

    const queryStub = Sinon.stub()
      .onCall(0).resolves({ recordset: [mockQuestionData1] })
      .onCall(1).resolves({ recordset: [mockOptionData1] })
      .onCall(2).resolves({ recordset: [mockQuestionData2] })
      .onCall(3).resolves({ recordset: [mockOptionData2] })

    const loadAssessment = await esmock('./assessment-loader-service.js', {
      '../mssql/session-questions/get.service.js': {
        default: getSessionQuestionsStub
      },
      '../mssql/session-options/get.service.js': {
        getSessionOptionsForQuestion: getSessionOptionsForQuestionStub
      },
      '../mssql/questions/get-child-questions.service.js': {
        default: getChildrenForQuestionStub
      },
      '@lcs/mssql-utility': {
        default: {
          getPool: () => ({
            request: () => ({
              input: Sinon.stub().returnsThis(),
              query: queryStub
            })
          })
        }
      }
    })

    const result = await loadAssessment.default(mockSessionId)

     // Should return sections with questions
    expect(result).to.be.an('array')
    expect(result).to.have.length(1) // One section
 
    const section = result[0]
    expect(section.SectionId).to.equal(mockSectionId)
    expect(section.Version).to.equal(1)
    expect(section.Title).to.equal('Test Section')
    expect(section.DisplayIndex).to.equal(1)
    expect(section.Questions).to.have.length(2)
 
    // Check first question
    expect(section.Questions[0].Id).to.equal(mockQuestionId1)
    expect(section.Questions[0].Stem).to.equal('Test Question 1')
    expect(section.Questions[0].Options).to.have.length(1)
    expect(section.Questions[0].Options[0].Id).to.equal(mockOptionId1)
 
    // Check second question
    expect(section.Questions[1].Id).to.equal(mockQuestionId2)
    expect(section.Questions[1].Stem).to.equal('Test Question 2')
    expect(section.Questions[1].Options).to.have.length(1)
    expect(section.Questions[1].Options[0].Id).to.equal(mockOptionId2)
  })

  it('should return empty array when no session questions found', async () => {
    const getSessionQuestionsStub = Sinon.stub().resolves([])

    const loadAssessment = await esmock('./assessment-loader-service.js', {
      '../mssql/session-questions/get.service.js': {
        default: getSessionQuestionsStub
      }
    })

    const result = await loadAssessment.default(mockSessionId)

    expect(result).to.be.an('array')
    expect(result).to.have.length(0)
  })

  it('should handle questions with sub-questions', async () => {
    const mockSubQuestionId = uuid()
    const mockSubOptionId = uuid()
    const mockSubQuestion: QuestionVersion = {
      Id: mockSubQuestionId,
      Version: 1,
      Stem: 'Sub Question',
      QuestionTypeId: 2
    } as QuestionVersion

    const mockSubOptionData: OptionVersion = {
      Id: mockSubOptionId,
      Version: 1,
      Text: 'Sub Option'
    } as OptionVersion

    const mockSubSessionOption = new SessionOptionModel({
      SessionId: mockSessionId,
      QuestionId: mockSubQuestionId,
      QuestionVersion: 1,
      OptionId: mockSubOptionId,
      OptionVersion: 1,
      PresentationIndex: 0
    })

    const getSessionQuestionsStub = Sinon.stub().resolves([mockSessionQuestions[0]])
    const getSessionOptionsForQuestionStub = Sinon.stub()
      .withArgs(mockSessionId, mockQuestionId1, 1).resolves([mockSessionOptions[0]])
      .withArgs(mockSessionId, mockSubQuestionId, 1).resolves([mockSubSessionOption])
    const getChildrenForQuestionStub = Sinon.stub().resolves([mockSubQuestion])

    const queryStub = Sinon.stub()
      .onCall(0).resolves({ recordset: [mockQuestionData1] })
      .onCall(1).resolves({ recordset: [mockOptionData1] })
      .onCall(2).resolves({ recordset: [mockSubOptionData] })

    const loadAssessment = await esmock('./assessment-loader-service.js', {
      '../mssql/session-questions/get.service.js': {
        default: getSessionQuestionsStub
      },
      '../mssql/session-options/get.service.js': {
        getSessionOptionsForQuestion: getSessionOptionsForQuestionStub
      },
      '../mssql/questions/get-child-questions.service.js': {
        default: getChildrenForQuestionStub
      },
      '@lcs/mssql-utility': {
        default: {
          getPool: () => ({
            request: () => ({
              input: Sinon.stub().returnsThis(),
              query: queryStub
            })
          })
        }
      }
    })

    const result = await loadAssessment.default(mockSessionId)
    // Should return one section with one question
    expect(result).to.be.an('array')
    expect(result).to.have.length(1)
    expect(result[0].Questions).to.have.length(1)

    // Check that the question has sub-questions
    const question = result[0].Questions[0]
    expect(question.SubQuestions).to.have.length(1)
    expect(question.SubQuestions![0].Id).to.equal(mockSubQuestionId)
    expect(question.SubQuestions![0].Options).to.have.length(1)
    expect(question.SubQuestions![0].Options[0].Id).to.equal(mockSubOptionId)
  
  })

  it('should handle errors gracefully', async () => {
    const getSessionQuestionsStub = Sinon.stub().rejects(new Error('Database error'))

    const loadAssessment = await esmock('./assessment-loader-service.js', {
      '../mssql/session-questions/get.service.js': {
        default: getSessionQuestionsStub
      }
    })

    try {
      await loadAssessment.default(mockSessionId)
      expect.fail('Should have thrown an error')
    } catch (error) {
      expect(error).to.be.instanceOf(Error)
      expect((error as Error).message).to.equal('Database error')
    }
  })
})
