import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import gradeMultiSelect from './grade-multi-select.js'
import gradeText from './grade-text.js'
import { QuestionTypes } from '@tess-f/sql-tables/dist/evaluations/question-type.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import createQuestionResponse from '../../mssql/question-response/create.service.js'
import saveQuestionScore from '../../mssql/session-question-scores/save-question-score.service.js'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'
import { EventType } from '@tess-f/shared-config'

const log = logger.create('Service-Internal.Grade-Fill-In-The-Blank')

/**
 * Grade fill-in-the-blank questions
 * This is a question of questions where:
 * - The root question has no options and contains a Stem with missing pieces
 * - The root question has sub-questions representing each blank
 * - Sub-questions can be dropdown (multiple choice) or text question types
 * 
 * @param question The question with sub-questions (no options on root)
 * @param userResponses The user's responses to sub-questions
 * @param sessionId
 * @returns Grading result with correctness and points earned
 */
export default async function gradeFillInTheBlank(
  question: QuestionWithOptions,
  userResponses: QuestionResponseModel[],
  sessionId: string
): Promise<SessionQuestionScoreModel> {
  // Must have sub-questions representing the blanks
  if (!question.SubQuestions || question.SubQuestions.length === 0) {
    // store the users responses
    await Promise.all(userResponses.map(async (response) => await createQuestionResponse(response)))
    // set the score pending
    log('error', 'Failed to grade question, no blanks defined', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: false })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Correct: false,
      Pending: true,
      PartiallyCorrect: false
    }))
  }

  // No responses means incorrect
  if (userResponses.length === 0) {
    // set the score to 0
    log('info', 'Cannot grade question, no user response. Setting score to 0.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Correct: false,
      Pending: false,
      PartiallyCorrect: false
    }))
  }

  let totalCorrect = 0
  const totalSubQuestions = question.SubQuestions.length

  // Grade each sub-question (blank)
  for (const subQuestion of question.SubQuestions) {
    // Find responses for this sub-question
    const subResponses = userResponses.filter(response => 
      response.fields.QuestionId === subQuestion.Id
    )

    // Grade based on sub-question type
    if (subQuestion.QuestionTypeId === QuestionTypes.Text) {
      // Text input blank
      const result = await gradeText(subQuestion, subResponses, sessionId)
      if (result.fields.Correct) {
        totalCorrect++
      }
    } else {
      // Dropdown blank (multiple choice)
      const result = await gradeMultiSelect(subQuestion, subResponses, sessionId)
      if (result.fields.Correct) {
        totalCorrect++
      }
    }
  }

  // Calculate points (partial credit for partially correct answers)
  if (totalCorrect === totalSubQuestions) {
    // the question is correct
    log('info', 'Successfully graded question, user response is correct.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      SessionId: sessionId,
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      Score: question.Points ?? 1,
      Pending: false,
      Correct: true,
      PartiallyCorrect: false
    }))
  } else if (totalCorrect > 0 && question.EnablePartialCredit) {
    // the question is partly correct
    log('info', 'Successfully graded question, user response is partially correct.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true, percentageCorrect: Math.round(totalCorrect / totalSubQuestions) })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      SessionId: sessionId,
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      Score: Math.round((totalCorrect / totalSubQuestions) * (question.Points ?? 1)),
      Correct: false,
      PartiallyCorrect: true,
      Pending: false
    }))
  } else {
    // the question is wrong is partially correct byt partiality is not enabled
    log('info', 'Successfully graded question, user response is incorrect.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      SessionId: sessionId,
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      Score: 0,
      Pending: false,
      PartiallyCorrect: false,
      Correct: false
    }))
  }
}
