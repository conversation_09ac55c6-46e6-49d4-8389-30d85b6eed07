import logger from '@lcs/logger'
import type { Request, Response } from 'express'
import httpStatus from 'http-status'
import getEvaluation from '../../../services/mssql/evaluations/get.service.js'
import getAllQuestionsForEvaluation from '../../../services/mssql/questions/get-all-for-evaluation.service.js'
import pickQuestions from '../../../services/internal/question-picker-service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z, ZodError } from 'zod'
import assessmentGenerator from '../../../services/internal/assessment-generator-service.js'
import loadAssessment from '../../../services/internal/assessment-loader-service.js'
import { sessionHasAssessmentData } from '../../../services/mssql/session/get.service.js'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import { EvaluationSectionWithQuestions } from '@tess-f/evaluations/dist/common/evaluation-section.js'
import { EventType } from '@tess-f/shared-config/dist/tess-config/logger.js'
import type { CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'

const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
const log = logger.create('Controller-HTTP.get-evaluation', httpLogTransformer)

async function getAssessmentQuestions(
  evaluation: CurrentEvaluationView,
  session: string,
  userId: string,
  req: Request
): Promise<QuestionWithOptions[] | EvaluationSectionWithQuestions[]> {
  const hasAssessmentData = await sessionHasAssessmentData(session)

  if (hasAssessmentData) {
    const evalQuestions = await loadAssessment(session)
    log('info', 'Successfully loaded existing assessment', {
      sessionId: session,
      questionsCount: evalQuestions.length,
      success: true,
      req,
      eventType: EventType.evaluation_get
    })
    return evalQuestions
  }

  const sectionsWithQuestions = await assessmentGenerator(userId, evaluation, session)
  const totalQuestions = sectionsWithQuestions.reduce((sum, section) => sum + section.Questions.length, 0)
  log('info', 'Successfully generated and saved new assessment', {
    sessionId: session,
    questionsCount: totalQuestions,
    sectionsCount: sectionsWithQuestions.length,
    success: true,
    req,
    eventType: EventType.assignments_create
  })
  return sectionsWithQuestions
}

async function getChecklistQuestions(
  evaluation: CurrentEvaluationView,
  req: Request
): Promise<QuestionWithOptions[]> {
  const allQuestions = await getAllQuestionsForEvaluation(evaluation.Id ?? '', evaluation.Version ?? 1)
  log('info', 'Successfully fetched all questions for evaluation', { success: true, count: allQuestions.length, req, eventType: EventType.question_bank_get })

  const evalQuestions = pickQuestions(allQuestions, {
    EqualizeObjectives: evaluation.EqualizeObjectives ?? false,
    EqualizeQuestions: evaluation.EqualizeQuestions ?? false,
    IncludeAllQuestions: evaluation.IncludeAllQuestions ?? false,
    NumberOfQuestions: evaluation.NumberOfQuestions ?? allQuestions.length
  })
  log('info', 'Successfully selected questions for evaluation', { count: evalQuestions.length, success: true, req, eventType: EventType.question_bank_get })

  if (!evaluation.RandomizeQuestions) {
    evalQuestions.sort((a, b) => (a.OrderId ?? 1) - (b.OrderId ?? 1))
  }

  return evalQuestions
}

function sanitizeQuestionOptions(questions: QuestionWithOptions[]): void {
  for (const question of questions) {
    for (const option of question.Options) {
      option.Correct = undefined
    }
    if (question.SubQuestions) {
      for (const subQuestion of question.SubQuestions) {
        for (const subOption of subQuestion.Options) {
          subOption.Correct = undefined
        }
      }
    }
  }
}

function sanitizeEvaluationQuestions(evalQuestions: QuestionWithOptions[] | EvaluationSectionWithQuestions[]): void {
  if (Array.isArray(evalQuestions) && evalQuestions.length > 0) {
    if ('Questions' in evalQuestions[0]) {
      // It's SectionWithQuestions[]
      const sections = evalQuestions as EvaluationSectionWithQuestions[]
      for (const section of sections) {
        sanitizeQuestionOptions(section.Questions)
      }
    } else {
      // It's QuestionWithOptions[]
      sanitizeQuestionOptions(evalQuestions as QuestionWithOptions[])
    }
  }
}

export default async function getEvaluationHandler(req: Request, res: Response): Promise<void> {
  try {
    const { id, session } = z.object({
      id: zodGUID,
      session: zodGUID.optional()
    }).parse(req.params)

    const evaluation = await getEvaluation(id)
    log('info', 'Successfully fetched evaluation', { evalId: evaluation.Id, success: true, req, eventType: EventType.evaluation_get })

    let evalQuestions: QuestionWithOptions[] | EvaluationSectionWithQuestions[] = []

    if (evaluation.EvaluationTypeId === 1) {
      if (!session) {
        res.status(BAD_REQUEST).send("Missing session")
        return
      }
      evalQuestions = await getAssessmentQuestions(evaluation, session, req.session.userId, req)
    } else {
      evalQuestions = await getChecklistQuestions(evaluation, req)
    }

    // Sanitize question options based on the type of result
    sanitizeEvaluationQuestions(evalQuestions)
    log('verbose', 'Set the option responses Correct fields to undefined', { success: true, req, eventType: EventType.question_options_modified })

    res.json({ evaluation, questions: evalQuestions })
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Invalid request data', { errorMessage: zodErrorToMessage(error), success: false, req, eventType: EventType.input_validation_errors })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
      log('error', 'Failed to get evaluation data', { error, success: false, evalId: req.params.id, req, eventType: EventType.evaluation_get })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
