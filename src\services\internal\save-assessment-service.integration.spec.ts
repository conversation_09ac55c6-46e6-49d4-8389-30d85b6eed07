import { expect } from 'chai'
import logger from '@lcs/logger'
import type { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import type { SaveAssessmentRequest } from '../../models/internal/save-assessment.js'
import saveAssessmentService from './save-assessment-service.js'
import mssql from '@lcs/mssql-utility'
import settings from '../../config/settings.js'

/** IMPORTANT must change xit to it to run service and replace DB values */

describe('Save Assessment Service - Integration Tests', () => {
  let createdSessionIds: string[] = []

  before(async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    logger.init({ level: 'silly' })
  })

  afterEach(async () => {
    // Clean up any created test data
    for (const sessionId of createdSessionIds) {
      try {
        // Clean up in reverse order: options -> questions -> session
        await mssql.getPool().request()
          .input('sessionId', sessionId)
          .query('DELETE FROM EVAL_SessionOptions WHERE SessionId = @sessionId')
        
        await mssql.getPool().request()
          .input('sessionId', sessionId)
          .query('DELETE FROM EVAL_SessionQuestions WHERE SessionId = @sessionId')
        
        await mssql.getPool().request()
          .input('sessionId', sessionId)
          .query('DELETE FROM EVAL_Sessions WHERE Id = @sessionId')
      } catch (error) {
        console.warn(`Failed to clean up session ${sessionId}:`, error)
      }
    }
    createdSessionIds = []
  })

  // create test data in the database or use existing valid IDs
  const testEvalId = '2264433E-F36B-1410-8023-004AC39F4610' // Replace with valid eval ID
  const testUserId = '36B96252-98B8-4F99-A463-EEF6A569539F' // Replace with valid user ID if needed
  const testQuestionId1 = '4562433E-F36B-1410-8023-004AC39F4610'// Replace with valid question ID
  const testQuestionId2 = 'A8FA423E-F36B-1410-8025-004AC39F4610' // Replace with valid question ID
  const testOptionId1 = 'ADFA423E-F36B-1410-8025-004AC39F4610' // Replace with valid option ID
  const testOptionId2 = 'B2FA423E-F36B-1410-8025-004AC39F4610' // Replace with valid option ID
  const testOptionId3 = '64FB423E-F36B-1410-8025-004AC39F4610' // Replace with valid option ID

  const mockQuestions: QuestionWithOptions[] = [
    {
      Id: testQuestionId1,
      Version: 1,
      Options: [
        { Id: testOptionId1, Version: 1 },
        { Id: testOptionId2, Version: 1 }
      ]
    } as QuestionWithOptions,
    {
      Id: testQuestionId2,
      Version: 1,
      Options: [
        { Id: testOptionId3, Version: 1 }
      ]
    } as QuestionWithOptions
  ]

  const validRequest: SaveAssessmentRequest = {
    evalId: testEvalId,
    evalVersion: 1,
    userId: testUserId,
    questions: mockQuestions,
    notes: 'Integration test assessment',
    sessionId: ''
  }

  xit('should actually save an assessment to the database', async () => {
    const result = await saveAssessmentService(validRequest)
    
    // Track for cleanup
    createdSessionIds.push(result.sessionId)
    
    // Verify the result structure
    expect(result.sessionId).to.be.a('string')
    expect(result.questionsCount).to.equal(2)
    expect(result.optionsCount).to.equal(3)

    // Verify session was actually created in database
    const sessionCheck = await mssql.getPool().request()
      .input('sessionId', result.sessionId)
      .query('SELECT * FROM EVAL_Sessions WHERE Id = @sessionId')

    expect(sessionCheck.recordset).to.have.length(1)
    expect(sessionCheck.recordset[0].EvalId).to.equal(testEvalId)
    expect(sessionCheck.recordset[0].UserId).to.equal(testUserId)
    expect(sessionCheck.recordset[0].Notes).to.equal('Integration test assessment')

    // Verify questions were created with correct presentation indices
    const questions = await mssql.getPool().request()
      .input('sessionId', result.sessionId)
      .query('SELECT * FROM EVAL_SessionQuestions WHERE SessionId = @sessionId ORDER BY PresentationIndex')
    
    expect(questions.recordset).to.have.length(2)
    expect(questions.recordset[0].QuestionId).to.equal(testQuestionId1)
    expect(questions.recordset[0].PresentationIndex).to.equal(0)
    expect(questions.recordset[1].QuestionId).to.equal(testQuestionId2)
    expect(questions.recordset[1].PresentationIndex).to.equal(1)

    // Verify options were created with correct presentation indices
    const options = await mssql.getPool().request()
      .input('sessionId', result.sessionId)
      .query('SELECT * FROM EVAL_SessionOptions WHERE SessionId = @sessionId ORDER BY QuestionId, PresentationIndex')
    
    expect(options.recordset).to.have.length(3)
    
    // First question options
    expect(options.recordset[0].OptionId).to.equal(testOptionId1)
    expect(options.recordset[0].PresentationIndex).to.equal(0)
    expect(options.recordset[1].OptionId).to.equal(testOptionId2)
    expect(options.recordset[1].PresentationIndex).to.equal(1)
    
    // Second question option
    expect(options.recordset[2].OptionId).to.equal(testOptionId3)
    expect(options.recordset[2].PresentationIndex).to.equal(0)
  })

  xit('should handle questions without options in database', async () => {
    const questionsWithoutOptions: QuestionWithOptions[] = [
      {
        Id: testQuestionId1,
        Version: 1,
        Options: []
      } as QuestionWithOptions
    ]

    const requestWithoutOptions: SaveAssessmentRequest = {
      ...validRequest,
      questions: questionsWithoutOptions
    }

    const result = await saveAssessmentService(requestWithoutOptions)
    
    // Track for cleanup
    createdSessionIds.push(result.sessionId)

    expect(result.questionsCount).to.equal(1)
    expect(result.optionsCount).to.equal(0)

    // Verify in database
    const options = await mssql.getPool().request()
      .input('sessionId', result.sessionId)
      .query('SELECT * FROM EVAL_SessionOptions WHERE SessionId = @sessionId')
    
    expect(options.recordset).to.have.length(0)
  })

})
