{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Program",
      "runtimeArgs": [
          "-r",
          "ts-node/register"
      ],
      "args": [
          "${workspaceFolder}/src/index.ts"
      ],
      "env": { "NODE_ENV": "dev", "LOG_LEVEL": "debug", "LOAD_DEV_ENV": "true", "CONFIG_PATHS":"./tess-config.yaml" }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Server",
      "console": "integratedTerminal",
      "runtimeArgs": [
          "-r",
          "ts-node/register"
      ],
      "args": [
          "${workspaceFolder}/src/index.ts"
      ],
      "env": {
        "LOAD_DEV_ENV": "true",
        "CONFIG_PATHS": "${workspaceFolder}/tess-config.yaml"
      }
    }, 
    {
      "type": "node",
      "request": "launch",
      "name": "Mocha All",
      "program": "${workspaceFolder}/node_modules/ts-mocha/bin/ts-mocha",
      "args": [
        "-r",
        "ts-node/register",
        "--timeout",
        "999999",
        "--colors",
        "${workspaceFolder}/**/*.spec.js",
        "--exit"
      ],
      "env": { "NODE_ENV": "dev", "LOG_LEVEL": "debug", "LOAD_DEV_ENV": "true", "CONFIG_PATHS":"./test-config.yaml" },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Mocha Current File",
      "program": "${workspaceFolder}/node_modules/ts-mocha/bin/ts-mocha",
      "args": [
        "--timeout",
        "999999",
        "--colors",
        "${file}",
        "-r",
        "ts-node/register",
        "--exit"
      ],
      "env": { "NODE_ENV": "dev", "LOG_LEVEL": "debug", "LOAD_DEV_ENV": "true", "CONFIG_PATHS":"./test-config.yaml" },
      "console": "integratedTerminal",
      "sourceMaps": true,
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
