import { Router } from 'express'

// controllers
import initSessionController from './init-session.controller.js'
import getSessionDataController from './get-session-data.controller.js'
import submitSessionController from './submit-session.controller.js'

// middlewares
import checkSessionMiddleware from '../middlewares/check-session.middleware.js'

const router = Router()

router.post('/init/:id', initSessionController)

router.use(checkSessionMiddleware)

router.get('/session-data/:id', getSessionDataController)
router.post('/submit/:id', submitSessionController)

export default router
