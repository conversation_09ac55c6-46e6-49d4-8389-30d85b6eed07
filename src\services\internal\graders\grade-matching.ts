import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import createQuestionResponse from '../../mssql/question-response/create.service.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import saveQuestionScore from '../../mssql/session-question-scores/save-question-score.service.js'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'
import { EventType } from '@tess-f/shared-config'

const log = logger.create('Service-Internal.Grade-Matching')
/**
 * Grade matching questions
 * User must match options to their correct targets.
 * Each response has an OptionId (the item being matched) and TargetOptionId (what it's matched to).
 * 
 * @param question The question with options
 * @param QuestionResponse The user's responses (one per match with OptionId and TargetOptionId)
 * @param sessionId
 * @returns Grading result with correctness and points earned
 */
export default async function gradeMatching(
  question: QuestionWithOptions,
  userResponses: QuestionResponseModel[],
  sessionId: string
): Promise<SessionQuestionScoreModel> {
  // Get all options that need to be matched (non targets, non distractors)
  const matchableOptions = question.Options.filter(opt => !opt.IsTarget && opt.TargetOptionId)
  
  if (matchableOptions.length === 0) {
    // No matchable options defined - this should not happen
    // save the users responses
    await Promise.all(userResponses.map(async (response) => await createQuestionResponse(response)))
    log('error', 'Failed to grade question. Question has no matches set.', { questionId: question.Id, sessionId, success: false, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Correct: false,
      Score: 0,
      PartiallyCorrect: false,
      Pending: true
    }))
    
  }

  // Grade each response
  const correctMatches_count = userResponses.reduce((previous, response) => {
    const correctMatch = question.Options.find(option => option.Id === response.fields.OptionId && option.TargetOptionId === response.fields.TargetOptionId)
    if (correctMatch) {
      // the user choice is the correct match
      response.fields.Correct = true
      return previous + 1
    } else {
      // incorrect match
      response.fields.Correct = false
      return previous
    }
  }, 0)

  const numberOfTargets = question.Options.filter(option => option.IsTarget).length

  if (correctMatches_count === numberOfTargets) {
    // the user correctly matched all the options
    log('info', 'Successfully graded question. User response is correct', { questionId: question.Id, sessionId, success: true, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: question.Points ?? 1,
      Pending: false,
      Correct: true,
      PartiallyCorrect: false
    }))
  } else if (correctMatches_count > 0 && question.EnablePartialCredit) {
    // the user didn't get all the matches but they can get partial credit
    log('info', 'Successfully graded question. User response is partial correct', { questionId: question.Id, sessionId, success: true, percentageCorrect: Math.round(correctMatches_count / numberOfTargets), eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: Math.round((correctMatches_count / numberOfTargets) * (question.Points ?? 1)),
      Correct: false,
      PartiallyCorrect: true,
      Pending: false
    }))
  } else {
    // the user is wrong, no partial credit
    log('info', 'Successfully graded question. User response is incorrect', { questionId: question.Id, sessionId, success: true, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Correct: false,
      PartiallyCorrect: false,
      Pending: false
    }))
  }
}

