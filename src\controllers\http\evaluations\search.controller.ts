import logger from '@lcs/logger'
import type { Request, Response } from 'express'
import searchEvaluationItems from '../../../services/mssql/evaluations/search.service.js'
import type { EvaluationItemFilters } from '../../../models/internal/evaluation-item-filters.js'
import type { SortOptions } from '../../../models/internal/sort-options.js'
import type { ItemSearchOptions } from '../../../models/internal/item-search.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodSqlDate, zodGUID, zodSortBy, zodSortDirection, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'
import { EvaluationTypes } from '@tess-f/sql-tables/dist/evaluations/evaluation-type.js'
import { EventType } from '@tess-f/shared-config/dist/tess-config/logger.js'

const { INTERNAL_SERVER_ERROR, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP:search-evaluations', httpLogTransformer)

export default async function searchEvaluationsController (req: Request, res: Response): Promise<void> {
  try {
    const { createdAfter, createdBefore, createdByIds, evaluationTypeIds, modifiedAfter, modifiedBefore, modifiedByIds, search, sortBy, sortDirection, limit, offset, systemId } = z.object({
      createdAfter: zodSqlDate.optional(),
      createdBefore: zodSqlDate.optional(),
      createdByIds: z.array(zodGUID).optional(),
      evaluationTypeIds: z.array(z.enum(EvaluationTypes)).optional(),
      modifiedAfter: zodSqlDate.optional(),
      modifiedBefore: zodSqlDate.optional(),
      modifiedByIds: z.array(zodGUID).optional(),
      search: z.string().optional(),
      sortBy: zodSortBy.optional(),
      sortDirection: zodSortDirection.optional(),
      limit: zodLimit,
      offset: zodOffset,
      systemId: z.string().optional()
    }).parse(req.body)

    const filters: EvaluationItemFilters = {
      createdAfter,
      createdBefore,
      createdByIds,
      evaluationTypeIds,
      modifiedAfter,
      modifiedBefore,
      modifiedByIds
    }

    const searchOptions: ItemSearchOptions = {
      value: search,
      scope: 'any'
    }

    const sortOptions: SortOptions = {
      sortColumn: sortBy,
      sortDirection
    }

    const results = await searchEvaluationItems(limit, offset, systemId ?? 'unknown', searchOptions, filters, sortOptions)

    log('info', 'Successfully searched evaluations', { totalResults: results.totalRecords, count: results.items.length, success: true, req, eventType: EventType.evaluation_get })

    res.json(results)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Invalid request data', { errorMessage: zodErrorToMessage(error), success: false, eventType: EventType.input_validation_errors })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error))
    } else {
      log('error', 'Failed to search evaluations', { success: false, error, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
