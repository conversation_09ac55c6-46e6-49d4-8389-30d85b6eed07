import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import { Vec2 as Vector2, HotspotShape } from '@tess-f/evaluations/dist/common/hotspots.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'
import createQuestionResponse from '../../mssql/question-response/create.service.js'
import saveQuestionScore from '../../mssql/session-question-scores/save-question-score.service.js'
import { EventType } from '@tess-f/shared-config'

const log = logger.create('Service-Internal.Grade-HotSpot-Labeling')
/**
 * Check if point is inside circle
 */
function isPointInCircle(point: Vector2, center: Vector2, radius: number): boolean {
  const dx = point[0] - center[0]
  const dy = point[1] - center[1]
  const distanceSquared = Math.pow(dx, 2) + Math.pow(dy, 2)
  const radiusSquared = Math.pow(radius, 2)
  return distanceSquared <= radiusSquared
}

/**
 * Check if point is inside polygon using ray casting algorithm
 */
function isPointInPolygon(point: Vector2, polygon: Vector2[]): boolean {
  let inside = false
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    if (((polygon[i][1] > point[1]) !== (polygon[j][1] > point[1])) &&
        (point[0] < (polygon[j][0] - polygon[i][0]) * (point[1] - polygon[i][1]) / (polygon[j][1] - polygon[i][1]) + polygon[i][0])) {
      inside = !inside
    }
  }
  return inside
}

/**
 * Check if a point falls within any hotspot shapes for an option
 */
function isPointInHotspots(point: Vector2, shapes: HotspotShape[]): boolean {
  if (shapes.length === 0) return false
  
  for (const shape of shapes) {
    let isInShape = false
    
    if (shape.type === 'circle') {
      const circle = shape
      isInShape = isPointInCircle(point, circle.center, circle.radius)
    } else if (shape.type === 'polygon') {
      const polygon = shape
      isInShape = isPointInPolygon(point, polygon.points)
    }
    
    if (isInShape) {
      return true
    }
  }
  
  return false
}

/**
 * Validates and parses a user response for hotspot grading
 */
function validateUserResponse(response: QuestionResponseModel): { valid: false } | { valid: true, userPoint: Vector2 } {
  if (!response.fields.OptionId || !response.fields.ResponseText) {
    return { valid: false }
  }

  try {
    const userPoint: Vector2 = JSON.parse(response.fields.ResponseText)
    return { valid: true, userPoint }
  } catch {
    return { valid: false }
  }
}

/**
 * Finds the placed option for a response
 */
function findPlacedOption(question: QuestionWithOptions, response: QuestionResponseModel) {
  return question.Options?.find(
    opt => opt.Id === response.fields.OptionId
  )
}

/**
 * Grades a user response 
 */
function gradeResponse(question: QuestionWithOptions, response: QuestionResponseModel): boolean {
  const isValid = validateUserResponse(response)
  if (!isValid.valid) return false

  const placedOption = findPlacedOption(question, response)
  if (!placedOption?.HotspotShape) return false

  try {
    const hotspotShapes: HotspotShape[] = JSON.parse(placedOption.HotspotShape)
    return isPointInHotspots(isValid.userPoint, hotspotShapes)
  } catch {
    return false
  }
}

/**
 * Grade labeling/hotspot questions
 */
export default async function gradeHotSpots(
  question: QuestionWithOptions,
  userResponses: QuestionResponseModel[],
  sessionId: string
): Promise<SessionQuestionScoreModel> {
  const optionsWithHotspots = question.Options.filter(
    opt => opt.HotspotShape !== undefined)
  
  if (optionsWithHotspots.length === 0) {
    // we have no hotspot options to grade
    // save the users responses
    await Promise.all(userResponses.map(async (response) => await createQuestionResponse(response)))
    log('error', 'Failed to grade question, no options with hotspots defined', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: false })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      SessionId: sessionId,
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      Score: 0,
      Pending: true,
      PartiallyCorrect: false,
      Correct: false
    }))
  }

  let correctPlacements = 0
  
  for (const response of userResponses) {
    response.fields.Correct = gradeResponse(question, response)
    // save the response
    await createQuestionResponse(response)
    
    if (response.fields.Correct) {
      correctPlacements++
    }
  }

  if (correctPlacements === optionsWithHotspots.length) {
    // the user got the question correct
    log('info', 'Successfully graded question, user response is correct.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      SessionId: sessionId,
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      Correct: true,
      Score: question.Points ?? 1,
      PartiallyCorrect: false,
      Pending: false
    }))
  } else if (correctPlacements > 0 && question.EnablePartialCredit) {
    // the user go the question partially correct
    log('info', 'Successfully graded question, user response is partially correct.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true, percentageCorrect: Math.round(correctPlacements / optionsWithHotspots.length) })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      SessionId: sessionId,
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      Score: Math.round((correctPlacements / optionsWithHotspots.length) * (question.Points ?? 1)),
      Correct: false,
      PartiallyCorrect: true,
      Pending: false
    }))
  } else {
    // the user got the question wrong
    // or the user got the question partially correct but partial credit is not enabled
    log('info', 'Successfully graded question, user response is incorrect.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      SessionId: sessionId,
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      Score: 0,
      Pending: false,
      PartiallyCorrect: false,
      Correct: false
    }))
  }
}
