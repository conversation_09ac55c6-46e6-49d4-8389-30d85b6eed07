import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import createQuestionResponse from '../../mssql/question-response/create.service.js'
import saveQuestionScore from '../../mssql/session-question-scores/save-question-score.service.js'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'
import { EventType } from '@tess-f/shared-config'

const log = logger.create('Service-Internal.Grade-Default-Option')

/**
 * Default grader for TrueFalse, MultipleChoice, and CheckAll questions
 * Supports partial credit for CheckAll questions
 */
export default async function gradeDefaultQuestionType(
  question: QuestionWithOptions,
  userResponses: QuestionResponseModel[],
  sessionId: string
): Promise<SessionQuestionScoreModel> {
  const correctOptions = question.Options.filter(option => option.Correct)
  const incorrectOptions = question.Options.filter(option => !option.Correct)
  const userSelectedOptions = userResponses
    .filter(response => response.fields.OptionId)
    .map(response => response.fields.OptionId!)

  const isCorrect = correctOptions.length === userSelectedOptions.length &&
    correctOptions.every(option => userSelectedOptions.includes(option.Id!)) &&
    !incorrectOptions.some(option => userSelectedOptions.includes(option.Id!))

  const partialCorrect = correctOptions.some(option => userSelectedOptions.includes(option.Id!))

  // Mark individual responses
  for (const response of userResponses) {
    response.fields.Correct = correctOptions.map(option => option.Id!).includes(response.fields.OptionId ?? '')
    await createQuestionResponse(response)
  }
  
  // store question score
  if (isCorrect) {
    // create score with full points
    log('info', 'Successfully graded question, user response is correct.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: question.Points ?? 1,
      Pending: false,
      PartiallyCorrect: false,
      Correct: true
    }))
  } else if (!isCorrect && partialCorrect && question.EnablePartialCredit) {
    // create score with partial credit
    const correctSelections = userSelectedOptions.filter(selectedOptionId => correctOptions.some(correctOption => correctOption.Id === selectedOptionId)).length
    const incorrectSelections = userSelectedOptions.filter(selectedOptionId => incorrectOptions.some(incorrectOption => incorrectOption.Id === selectedOptionId)).length

    let pointsPercentage = 1
    if (incorrectOptions.length === 0) {
      pointsPercentage = correctSelections / correctOptions.length
    } else {
      pointsPercentage = (correctSelections / correctOptions.length) * (1 - incorrectSelections / incorrectOptions.length)
    }

    log('info', 'Successfully graded question, user response is partially correct.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: Math.round(pointsPercentage * (question.Points ?? 1)),
      Pending: false,
      PartiallyCorrect: true,
      Correct: false
    }))
  } else {
    // create score with 0 points
    log('info', 'Successfully graded question, user response is incorrect.', { questionId: question.Id, sessionId, eventType: EventType.question_grade, success: true })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Pending: false,
      PartiallyCorrect: false,
      Correct: false
    }))
  }
}
