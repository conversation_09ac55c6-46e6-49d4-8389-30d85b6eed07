import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import getSessionQuestions from '../mssql/session-questions/get.service.js'
import { getSessionOptionsForQuestion } from '../mssql/session-options/get.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import mssql from '@lcs/mssql-utility'
import { type QuestionVersion, QuestionVersionViewName, QuestionVersionFields } from '@tess-f/sql-tables/dist/evaluations/question-version-view.js'
import { type OptionVersion, OptionVersionViewName, OptionVersionFields } from '@tess-f/sql-tables/dist/evaluations/option-version-view.js'
import getChildrenForQuestion from '../mssql/questions/get-child-questions.service.js'
import { EventType } from '@tess-f/shared-config'
import type { SessionQuestionModel } from '../../models/session-question.model.js'
import type { SessionOptionModel } from '../../models/session-option.model.js'
import { EvaluationSectionWithQuestions } from '@tess-f/evaluations/dist/common/evaluation-section.js'

const log = logger.create('assessment-loader-service')

/**
 * Loads an existing assessment session and returns the questions organized by sections
 * in the same format as assessmentGenerator would return them. This allows viewing
 * previously generated assessments with their original question order, section structure,
 * and options.
 *
 * @param sessionId The session ID of the saved assessment
 * @returns Promise containing the ordered list of sections with their questions
 */
export default async function loadAssessment (sessionId: string): Promise<EvaluationSectionWithQuestions[]> {
  try {
    log('info', 'Starting assessment load process', { sessionId, eventType: EventType.assignments_get })

    // Get all session questions ordered by presentation index
    const sessionQuestions = await getSessionQuestions(sessionId)
    log('info', 'Retrieved session questions', { count: sessionQuestions.length, sessionId, eventType: EventType.question_get })

    if (sessionQuestions.length === 0) {
      log('warn', 'No questions found for session', { sessionId, eventType: EventType.assignments_get })
      return []
    }

    // Group questions by section and build them with their options
    const sectionMap = new Map<string, {
      sectionId: string
      sectionVersion: number
      sectionTitle: string
      sectionDisplayIndex: number
      questions: QuestionWithOptions[]
    }>()
    
    const processedQuestionIds = new Set<string>()

    for (const sessionQuestion of sessionQuestions) {
      const questionKey = `${sessionQuestion.fields.QuestionId}-${sessionQuestion.fields.QuestionVersion}`

      // Skip if we've already processed this question (could be a sub-question)
      if (processedQuestionIds.has(questionKey)) {
        continue
      }

      const questionWithOptions = await buildQuestionWithOptions(
        sessionQuestion,
        sessionId,
        processedQuestionIds
      )

      if (questionWithOptions) {
        processedQuestionIds.add(questionKey)

        // Get section information from the session question
        const sectionId = sessionQuestion.fields.SectionId
        const sectionVersion = sessionQuestion.fields.SectionVersion
        const sectionTitle = sessionQuestion.fields.SectionTitle
        const sectionDisplayIndex = sessionQuestion.fields.SectionDisplayIndex

        // If section information is missing, log a warning and skip
        if (!sectionId || sectionVersion === undefined || sectionDisplayIndex === undefined) {
          log('warn', 'Missing section information for question', {
            questionId: sessionQuestion.fields.QuestionId,
            questionVersion: sessionQuestion.fields.QuestionVersion,
            sessionId,
            eventType: EventType.question_get
          })
          continue
        }

        // Create section key
        const sectionKey = `${sectionId}-${sectionVersion}`

        // Initialize section if it doesn't exist
        if (!sectionMap.has(sectionKey)) {
          sectionMap.set(sectionKey, {
            sectionId,
            sectionVersion,
            sectionTitle: sectionTitle ?? '',
            sectionDisplayIndex,
            questions: []
          })
        }

        // Add question to the section
        sectionMap.get(sectionKey)!.questions.push(questionWithOptions)
      }
    }
      
    // Convert the section map to an array of EvaluationSectionWithQuestions, sorted by display index
    const sectionsWithQuestions: EvaluationSectionWithQuestions[] = Array.from(sectionMap.values())
    .sort((a, b) => a.sectionDisplayIndex - b.sectionDisplayIndex)
    .map(section => ({
      SectionId: section.sectionId,
      Version: section.sectionVersion,
      Title: section.sectionTitle,
      DisplayIndex: section.sectionDisplayIndex,
      Questions: section.questions
    }))
  
    log('info', 'Successfully loaded assessment', {
      sessionId,
      sectionsCount: sectionsWithQuestions.length,
      totalQuestionsCount: sectionsWithQuestions.reduce((sum, section) => sum + section.Questions.length, 0),
      eventType: EventType.assignments_get
    })

    return sectionsWithQuestions

  } catch (error) {
    log('error', 'Failed to load assessment', {
      error: getErrorMessage(error),
      sessionId,
      eventType: EventType.assignments_get
    })
    throw error
  }
}

/**
 * Build a question with its options and sub-questions
 */
async function buildQuestionWithOptions (
  sessionQuestion: SessionQuestionModel,
  sessionId: string,
  processedQuestionIds: Set<string>
): Promise<QuestionWithOptions | null> {
  // Get the full question data from the question version view
  const questionData = await getQuestionData(
    sessionQuestion.fields.QuestionId!,
    sessionQuestion.fields.QuestionVersion!
  )

  if (!questionData) {
    log('warn', 'Question data not found', {
      questionId: sessionQuestion.fields.QuestionId,
      questionVersion: sessionQuestion.fields.QuestionVersion,
      sessionId,
      eventType: EventType.question_bank_get
    })
    return null
  }

  // Get the session options for this question (in presentation order)
  const sessionOptions = await getSessionOptionsForQuestion(
    sessionId,
    sessionQuestion.fields.QuestionId!,
    sessionQuestion.fields.QuestionVersion!
  )

  // Get the full option data for each session option
  const options = await buildOptionsForQuestion(sessionOptions)

  // Get sub-questions if they exist
  const subQuestions = await getChildrenForQuestion(questionData.Id!, questionData.Version!)
  const subQuestionsWithOptions = await buildSubQuestionsWithOptions(
    subQuestions,
    sessionId,
    processedQuestionIds
  )

  // Build the complete question with options
  return {
    ...questionData,
    Options: options,
    SubQuestions: subQuestionsWithOptions.length > 0 ? subQuestionsWithOptions : undefined
  }
}

/**
 * Build options for a question from session options
 */
async function buildOptionsForQuestion (sessionOptions: SessionOptionModel[]): Promise<OptionVersion[]> {
  const options: OptionVersion[] = []
  for (const sessionOption of sessionOptions) {
    const optionData = await getOptionData(
      sessionOption.fields.OptionId!,
      sessionOption.fields.OptionVersion!
    )
    if (optionData) {
      options.push(optionData)
    }
  }
  return options
}

/**
 * Build sub-questions with their options
 */
async function buildSubQuestionsWithOptions (
  subQuestions: QuestionVersion[],
  sessionId: string,
  processedQuestionIds: Set<string>
): Promise<QuestionWithOptions[]> {
  const subQuestionsWithOptions: QuestionWithOptions[] = []

  for (const subQuestion of subQuestions) {
    const subQuestionKey = `${subQuestion.Id}-${subQuestion.Version}`
    if (processedQuestionIds.has(subQuestionKey)) {
      continue
    }

    // Get session options for sub-question
    const subSessionOptions = await getSessionOptionsForQuestion(
      sessionId,
      subQuestion.Id!,
      subQuestion.Version!
    )

    const subOptions = await buildOptionsForQuestion(subSessionOptions)

    subQuestionsWithOptions.push({
      ...subQuestion,
      Options: subOptions
    })

    processedQuestionIds.add(subQuestionKey)
  }

  return subQuestionsWithOptions
}

/**
 * Get question data from the question version view
 */
async function getQuestionData (questionId: string, version: number): Promise<QuestionVersion | null> {
  const request = mssql.getPool().request()
  request.input('questionId', questionId)
  request.input('version', version)

  const results = await request.query<QuestionVersion>(`
    SELECT * FROM [${QuestionVersionViewName}]
    WHERE [${QuestionVersionFields.Id}] = @questionId 
      AND [${QuestionVersionFields.Version}] = @version
  `)

  return results.recordset.length > 0 ? results.recordset[0] : null
}

/**
 * Get option data from the option version view
 */
async function getOptionData (optionId: string, version: number): Promise<OptionVersion | null> {
  const request = mssql.getPool().request()
  request.input('optionId', optionId)
  request.input('version', version)

  const results = await request.query<OptionVersion>(`
    SELECT * FROM [${OptionVersionViewName}]
    WHERE [${OptionVersionFields.Id}] = @optionId 
      AND [${OptionVersionFields.Version}] = @version
  `)

  return results.recordset.length > 0 ? results.recordset[0] : null
}
