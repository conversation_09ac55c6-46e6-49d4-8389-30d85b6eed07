import mssql, { updateRow } from '@lcs/mssql-utility'
import type { SessionModel } from '../../../models/session.model.js'
import { EvaluationSession } from '@tess-f/sql-tables/dist/evaluations/session.js'

export default async function updateEvaluationSession (session: SessionModel): Promise<SessionModel> {
  const updated = await updateRow<EvaluationSession>(mssql.getPool().request(), session, { Id: session.fields.Id })
  session.importFromDatabase(updated[0])
  return session
}
