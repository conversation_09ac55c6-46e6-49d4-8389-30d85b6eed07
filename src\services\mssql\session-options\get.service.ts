import mssql from '@lcs/mssql-utility'
import { SessionOptionModel, type EvalSessionOption } from '../../../models/session-option.model.js'
import { SessionOptionsTableName } from '@tess-f/sql-tables/dist/evaluations/session-options.js'

/**
 * Get all session options for a specific session, grouped by question
 */
export async function getSessionOptions (sessionId: string): Promise<SessionOptionModel[]> {
  const request = mssql.getPool().request()
  request.input('sessionId', sessionId)
  
  const results = await request.query<EvalSessionOption>(`
    SELECT * FROM [${SessionOptionsTableName}]
    WHERE SessionId = @sessionId
    ORDER BY QuestionId, QuestionVersion, PresentationIndex ASC
  `)
  
  return results.recordset.map(record => new SessionOptionModel(undefined, record))
}

/**
 * Get session options for a specific question within a session
 */
export async function getSessionOptionsForQuestion (sessionId: string, questionId: string, questionVersion: number): Promise<SessionOptionModel[]> {
  const request = mssql.getPool().request()
  request.input('sessionId', sessionId)
  request.input('questionId', questionId)
  request.input('questionVersion', questionVersion)
  
  const results = await request.query<EvalSessionOption>(`
    SELECT * FROM [${SessionOptionsTableName}]
    WHERE SessionId = @sessionId 
      AND QuestionId = @questionId 
      AND QuestionVersion = @questionVersion
    ORDER BY PresentationIndex ASC
  `)
  
  return results.recordset.map(record => new SessionOptionModel(undefined, record))
}

export default getSessionOptions
