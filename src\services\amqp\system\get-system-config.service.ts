import rabbitMQ from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import { SystemConfig } from '@tess-f/system-config/dist/http/system-config.js'
import { RpcCommands } from '@tess-f/system-config/dist/amqp/rpc-commands.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-AMQP.get-system-config')

export async function getSystemConfig (): Promise<SystemConfig> {
  try {
    const response: RpcResponse<SystemConfig> = await rabbitMQ.executeRPC(
      settings.amqp.serviceQueues.system, {
        command: RpcCommands.GetSystemConfig
      },
      settings.amqp.rpc_timeout
    )

    if (response.success) {
      return response.data!
    } else {
      log('error', 'Failed to get system config', { success: false, messageFromRPC: response.message })
      throw new Error(response.message ?? 'Unknown error')
    }
  } catch (error) {
    log('error', 'Error executing rpc call', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
