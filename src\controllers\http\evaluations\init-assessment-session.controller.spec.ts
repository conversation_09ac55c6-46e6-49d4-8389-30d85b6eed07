import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import { SessionModel } from '../../../models/session.model.js'

describe('HTTP init assessment session controller', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  const mockEvaluation = {
    Id: uuid(),
    Title: 'Test Evaluation',
    Description: 'Test Description',
    Version: 1
  }

  const mockSessionRecord = {
    Id: uuid(),
    UserId: uuid(),
    EvalId: mockEvaluation.Id,
    EvalVersion: 1,
    Start: new Date()
  }

  it('creates new session and submits xAPI launch statement', async () => {
    const getEvaluationStub = Sinon.stub().resolves(mockEvaluation)
    const createEvaluationSessionStub = Sinon.stub().resolves(new SessionModel(undefined, mockSessionRecord))
    const getSystemConfigStub = Sinon.stub().resolves({ Domain: 'http://localhost' })
    const getUserStub = Sinon.stub().resolves({ FirstName: 'Test', LastName: 'User', Username: 'testuser' })
    const createStatementStub = Sinon.stub().resolves(uuid())

    const controller = await esmock('./init-assessment-session.controller.js', {
      '../../../services/mssql/evaluations/get.service.js': {
        default: getEvaluationStub
      },
      '../../../services/mssql/session/create.service.js': {
        default: createEvaluationSessionStub
      },
      '../../../services/mssql/user/get.service.js': {
        default: getUserStub
      },
      '../../../services/amqp/system/get-system-config.service.js': {
        getSystemConfig: getSystemConfigStub
      },
      '../../../services/amqp/lrs/create-statement.service.js': {
        default: createStatementStub
      }
    })

    const evaluationId = mockEvaluation.Id
    const userId = mockSessionRecord.UserId

    const mocks = httpMocks.createMocks({
      session: {
        userId
      },
      params: {
        id: evaluationId
      }
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.OK)

    const responseData = JSON.parse(mocks.res._getData())
    expect(responseData).to.have.property('sessionId')
    expect(responseData.sessionId).to.equal(mockSessionRecord.Id)
    expect(createStatementStub.called).to.equal(true)
  })

  it('returns bad request if evaluation ID is invalid', async () => {
    const getEvaluationStub = Sinon.stub().resolves(mockEvaluation)

    const controller = await esmock('./init-assessment-session.controller.js', {
      '../../../services/mssql/evaluations/get.service.js': {
        default: getEvaluationStub
      }
    })

    const mocks = httpMocks.createMocks({
      session: {
        userId: uuid()
      },
      params: {
        id: 'invalid-id'
      }
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const responseData = mocks.res._getData()
    expect(responseData).to.include('Invalid data')
  })

  it('returns internal server error if xAPI statement submission fails', async () => {
    const getEvaluationStub = Sinon.stub().resolves(mockEvaluation)
    const createEvaluationSessionStub = Sinon.stub().resolves(new SessionModel(undefined, mockSessionRecord))
    const getSystemConfigStub = Sinon.stub().resolves({ Domain: 'http://localhost' })
    const getUserStub = Sinon.stub().resolves({ FirstName: 'Test', LastName: 'User', Username: 'testuser' })
    const createStatementStub = Sinon.stub().rejects(new Error('xAPI error'))

    const controller = await esmock('./init-assessment-session.controller.js', {
      '../../../services/mssql/evaluations/get.service.js': {
        default: getEvaluationStub
      },
      '../../../services/mssql/session/create.service.js': {
        default: createEvaluationSessionStub
      },
      '../../../services/mssql/user/get.service.js': {
        default: getUserStub
      },
      '../../../services/amqp/system/get-system-config.service.js': {
        getSystemConfig: getSystemConfigStub
      },
      '../../../services/amqp/lrs/create-statement.service.js': {
        default: createStatementStub
      }
    })

    const evaluationId = mockEvaluation.Id
    const userId = mockSessionRecord.UserId

    const mocks = httpMocks.createMocks({
      session: {
        userId
      },
      params: {
        id: evaluationId
      }
    })

    await controller.default(mocks.req, mocks.res)

    // Should still return OK even if xAPI submission fails
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const responseData = JSON.parse(mocks.res._getData())
    expect(responseData).to.have.property('sessionId')
  })

  it('returns internal server error if database operation fails', async () => {
    const getEvaluationStub = Sinon.stub().rejects(new Error('Database error'))

    const controller = await esmock('./init-assessment-session.controller.js', {
      '../../../services/mssql/evaluations/get.service.js': {
        default: getEvaluationStub
      }
    })

    const mocks = httpMocks.createMocks({
      session: {
        userId: uuid()
      },
      params: {
        id: uuid()
      }
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
