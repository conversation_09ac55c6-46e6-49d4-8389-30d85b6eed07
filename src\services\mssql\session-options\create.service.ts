import mssql, { addRow } from '@lcs/mssql-utility'
import { SessionOptionModel, type EvalSessionOption } from '../../../models/session-option.model.js'

export default async function createSessionOption (sessionOption: SessionOptionModel): Promise<SessionOptionModel> {
  const record = await addRow<EvalSessionOption>(mssql.getPool().request(), sessionOption)
  return new SessionOptionModel(undefined, record)
}
