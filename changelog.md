# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog],
and this project adheres to [Semantic Versioning].

## [Unreleased](https://github.northgrum.com/LCS-TESS/Eval-Engine/compare/v1.0.1...development)

### Added

### Changed

### Deprecated

### Removed

### Fixed

### Security


## [v1.0.1](https://github.northgrum.com/LCS-TESS/Eval-Engine/compare/v1.0.0...v1.0.1)

### Added

- tess-f backend utils library (contains shared code for utilities such as clamping numbers, parsing error messages/stack trace, http log transforms, etc.)
- cookieSecure and cookieSigned
- Prometheus Metrics
- User input validation

### Changed

- Updated http controller logging to use httpLogTransformer from tess-f backend utils. All http controller and middlewares logs will now contain the following data:
  + ip address of incoming request
  + active user id
  + request headers
  + request method
  + active user session id
  + error message - for error objects
  + error stack trace - for error objects

### Removed

- Local utils, updated to tess-f backend utils library.

### Security

- Updated base image to one that supports FIPS
- Updated express to version 5

## [v1.0.0](https://github.northgrum.com/LCS-TESS/Eval-Engine/tree/v1.0.0)

Initial Release

<!-- Links -->
[keep a changelog]: https://keepachangelog.com/en/1.0.0/
[semantic versioning]: https://semver.org/spec/v2.0.0.html
