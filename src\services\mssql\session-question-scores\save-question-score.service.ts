import mssql, { addRow } from '@lcs/mssql-utility'
import { SessionQuestionScore } from '@tess-f/sql-tables/dist/evaluations/session-question-score.js'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'

/**
 * Save question score to database
 */
export default async function saveQuestionScore(questionScore: SessionQuestionScoreModel): Promise<SessionQuestionScoreModel> {
  const created = await addRow<SessionQuestionScore>(mssql.getPool().request(), questionScore)
  return new SessionQuestionScoreModel(undefined, created)
}
