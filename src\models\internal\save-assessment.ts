import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'

export interface SaveAssessmentRequest {
    /** The evaluation ID */
    evalId: string
    /** The evaluation version */
    evalVersion: number
    /** The user ID taking the assessment */
    userId: string
    /** The ordered list of questions with options as generated by assessment-generator-service */
    questions: QuestionWithOptions[]
    sessionId: string
    /** Optional notes for the session */
    notes?: string
  }
  
  export interface SaveAssessmentResponse {
    /** The created session ID */
    sessionId: string
    /** Number of questions saved */
    questionsCount: number
    /** Total number of options saved across all questions */
    optionsCount: number
  }