import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import { v4 as uuid } from 'uuid'

describe('Session Get Service', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  const mockSessionId = uuid()

  describe('sessionHasAssessmentData', () => {
    it('should return true when session has questions', async () => {
      const queryStub = Sinon.stub().resolves({
        recordset: [{ Count: 5 }]
      })
      
      const { sessionHasAssessmentData } = await esmock('./get.service.js', {
        '@lcs/mssql-utility': {
          default: {
            getPool: () => ({
              request: () => ({
                input: Sinon.stub().returnsThis(),
                query: queryStub
              })
            })
          }
        }
      })

      const result = await sessionHasAssessmentData(mockSessionId)

      expect(result).to.equal(true);
    })

    it('should return false when session has no questions', async () => {
      const queryStub = Sinon.stub().resolves({
        recordset: [{ Count: 0 }]
      })
      
      const { sessionHasAssessmentData } = await esmock('./get.service.js', {
        '@lcs/mssql-utility': {
          default: {
            getPool: () => ({
              request: () => ({
                input: Sinon.stub().returnsThis(),
                query: queryStub
              })
            })
          }
        }
      })

      const result = await sessionHasAssessmentData(mockSessionId)

      expect(result).to.equal(false);
    })
  })
})
