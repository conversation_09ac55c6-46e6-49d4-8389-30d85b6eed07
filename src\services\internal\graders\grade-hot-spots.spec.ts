import { describe, it, before } from 'mocha'
import { expect } from 'chai'
import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import Sinon from 'sinon'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import esmock from 'esmock'

describe('grade-hot-spots', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  const mockCircleQuestion: QuestionWithOptions = {
    Id: 'test-question-id',
    Version: 1,
    QuestionTypeId: 7,
    EnablePartialCredit: false,
    Points: 10,
    Options: [
      {
        Id: 'option-1',
        Version: 1,
        HotspotShape: '[{"type":"circle","center":[100,100],"radius":50}]'
      }
    ]
  }

  const mockPolygonQuestion: QuestionWithOptions = {
    Id: 'test-question-id',
    Version: 1,
    QuestionTypeId: 7,
    EnablePartialCredit: false,
    Points: 10,
    Options: [
      {
        Id: 'option-2',
        Version: 1,
        HotspotShape: '[{"type":"polygon","points":[[200,200],[250,200],[250,250],[200,250]]}]'
      }
    ]
  }

  const createResponse = (optionId: string, responseText: string): QuestionResponseModel => (new QuestionResponseModel({
    Id: `response-${optionId}`,
    OptionId: optionId,
    OptionVersion: 1,
    ResponseText: responseText
  }))

  it('should grade correct circle placement as correct', async () => {
    const responses = [createResponse('option-1', '[100,100]')]

    const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
    const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
      './grade-hot-spots.js', {
        '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
        '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
      }
    )

    await mock(mockCircleQuestion, responses, '10')
    expect(saveQuestionMock.called).to.equal(true)
    Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: mockCircleQuestion.Points ?? 1, SessionId: '10', Correct: true, PartiallyCorrect: false, Pending: false, QuestionId: mockCircleQuestion.Id, QuestionVersion: mockCircleQuestion.Version }))
  })

  it('should grade correct polygon placement as correct', async () => {
    const responses = [createResponse('option-2', '[225,225]')]
    const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
    const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
      './grade-hot-spots.js', {
        '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
        '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
      }
    )

    await mock(mockPolygonQuestion, responses, '10')
    expect(saveQuestionMock.called).to.equal(true)
    Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: mockPolygonQuestion.Points ?? 1, SessionId: '10', Correct: true, Pending: false, PartiallyCorrect: false, QuestionId: mockPolygonQuestion.Id, QuestionVersion: mockPolygonQuestion.Version }))
  })

  it('should grade incorrect placement as incorrect', async () => {
    const responses = [createResponse('option-1', '[200,200]')]
    const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
    const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
      './grade-hot-spots.js', {
        '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
        '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
      }
    )

    await mock(mockCircleQuestion, responses, '10')
    expect(saveQuestionMock.called).to.equal(true)
    Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: 0, SessionId: '10', Correct: false, Pending: false, PartiallyCorrect: false, QuestionId: mockCircleQuestion.Id, QuestionVersion: mockCircleQuestion.Version }))
  })

  it('should award full points when all hotspots are correctly placed', async () => {
    const responses = [
      createResponse('option-1', '[100,100]'),
      createResponse('option-2', '[225,225]')
    ]
    const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
    const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
      './grade-hot-spots.js', {
        '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
        '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
      }
    )

    await mock({ ...mockCircleQuestion, ...mockPolygonQuestion, Options: [...mockCircleQuestion.Options, ...mockPolygonQuestion.Options]}, responses, '10')
    expect(saveQuestionMock.called).to.equal(true)
    Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: 10, SessionId: '10', Correct: true, Pending: false, PartiallyCorrect: false, QuestionId: mockCircleQuestion.Id, QuestionVersion: mockCircleQuestion.Version }))
  })

  it('should handle partial credit when enabled', async () => {
    const questionWithPartialCredit: QuestionWithOptions = { ...mockCircleQuestion, Options: [...mockCircleQuestion.Options, ...mockPolygonQuestion.Options], EnablePartialCredit: true }
    const responses = [createResponse('option-1', '[100,100]')]
    const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
    const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
      './grade-hot-spots.js', {
        '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
        '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
      }
    )

    await mock(questionWithPartialCredit, responses, '10')
    expect(saveQuestionMock.called).to.equal(true)
    Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Correct: false, SessionId: '10', PartiallyCorrect: true, Score: 5, Pending: false, QuestionId: mockCircleQuestion.Id, QuestionVersion: mockCircleQuestion.Version }))
  })

  it('should handle invalid response text', async () => {
    const responses = [createResponse('option-1', 'invalid-json')]
    const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
    const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
      './grade-hot-spots.js', {
        '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
        '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
      }
    )

    await mock(mockCircleQuestion, responses, '10')
    expect(saveQuestionMock.called).to.equal(true)
    Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: 0, SessionId: '10', Pending: false, Correct: false, PartiallyCorrect: false, QuestionId: mockCircleQuestion.Id, QuestionVersion: mockCircleQuestion.Version }))
  })

  it('should handle questions with no hotspot options', async () => {
    const questionWithoutHotspots: QuestionWithOptions = {
      ...mockCircleQuestion,
      Options: [{ Id: 'option-1', Version: 1 }]
    }
    const responses = [createResponse('option-1', '[100,100]')]
    const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
    const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
      './grade-hot-spots.js', {
        '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
        '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
      }
    )

    await mock(questionWithoutHotspots, responses, '10')
    expect(saveQuestionMock.called).to.equal(true)
    Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: 0, SessionId: '10', Pending: true, Correct: false, PartiallyCorrect: false, QuestionId: mockCircleQuestion.Id, QuestionVersion: mockCircleQuestion.Version }))
  })

  it('should handle empty responses', async () => {
    const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
    const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
      './grade-hot-spots.js', {
        '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
        '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
      }
    )
    await mock(mockCircleQuestion, [], '10')
    expect(saveQuestionMock.called).to.equal(true)
    Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: 0, SessionId: '10', Pending: false, Correct: false, PartiallyCorrect: false, QuestionId: mockCircleQuestion.Id, QuestionVersion: mockCircleQuestion.Version }))
  })
})
