import { Table } from '@lcs/mssql-utility'
import { SessionQuestionScore, SessionQuestionScoreFields, EvaluationSectionTableName } from '@tess-f/sql-tables/dist/evaluations/session-question-score.js'

export default class SessionQuestionScoreModel extends Table<SessionQuestionScore, SessionQuestionScore> {
  fields: SessionQuestionScore

  constructor (fields?: SessionQuestionScore, record?: SessionQuestionScore) {
    super(
      EvaluationSectionTableName,
      [
        SessionQuestionScoreFields.SessionId,
        SessionQuestionScoreFields.QuestionId,
        SessionQuestionScoreFields.QuestionVersion,
        SessionQuestionScoreFields.Score
      ]
    )

    this.fields = fields ?? {}
    if (record) this.importFromDatabase(record)
  }

  importFromDatabase (record: SessionQuestionScore): void {
    this.fields = {
      SessionId: record.SessionId,
      QuestionId: record.QuestionId,
      QuestionVersion: record.QuestionVersion,
      Notes: record.Notes,
      Correct: record.Correct,
      Score: record.Score,
      PartiallyCorrect: record.PartiallyCorrect,
      Pending: record.Pending
    }
  }

  exportJsonToDatabase (): SessionQuestionScore {
    return {
      SessionId: this.fields.SessionId,
      QuestionId: this.fields.QuestionId,
      QuestionVersion: this.fields.QuestionVersion,
      Notes: this.fields.Notes,
      Correct: this.fields.Correct,
      Score: this.fields.Score,
      PartiallyCorrect: this.fields.PartiallyCorrect,
      Pending: this.fields.Pending
    }
  }
}
