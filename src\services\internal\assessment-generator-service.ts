import mssql from '@lcs/mssql-utility'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import { EvaluationSectionWithQuestions } from '@tess-f/evaluations/dist/common/evaluation-section.js'
import { EvaluationQuestionFields, EvaluationQuestionTableName} from '@tess-f/sql-tables/dist/evaluations/evaluation-questions.js'
import { CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'
import getOptionsForQuestion from '../mssql/question-options/get-for-question.service.js'
import getChildrenForQuestion from '../mssql/questions/get-child-questions.service.js'
import { QuestionVersion, QuestionVersionViewName, QuestionVersionFields } from '@tess-f/sql-tables/dist/evaluations/question-version-view.js'
import { type OptionVersion } from '@tess-f/sql-tables/dist/evaluations/option-version-view.js'
import { DynamicQuestionSetVersion, DynamicQuestionSetVersionFields, DynamicQuestionSetVersionTableName} from '@tess-f/sql-tables/dist/evaluations/dynamic-question-set-version.js'
import {  QuestionBankQuestionFields, QuestionBankQuestionsTableName } from '@tess-f/sql-tables/dist/evaluations/question-bank-question.js'
import {  DynamicQuestionSetsObjectivesFields, DynamicQuestionSetsObjectivesTableName } from '@tess-f/sql-tables/dist/evaluations/dynamic-question-sets-objectives.js'
import { QuestionBankFields, QuestionBanksTableName } from '@tess-f/sql-tables/dist/evaluations/question-bank.js'
import { QuestionObjectiveFields, QuestionsObjectivesTableName} from '@tess-f/sql-tables/dist/evaluations/question-objective.js'
import {  EvaluationSectionVersionFields, EvaluationSectionVersionTableName } from '@tess-f/sql-tables/dist/evaluations/section-version.js'
import { DynamicQuestionSetsBanksFields, DynamicQuestionSetsBanksTableName} from '@tess-f/sql-tables/dist/evaluations/dynamic-question-sets-banks.js'
import {DynamicQuestionSetsTypesFields, DynamicQuestionSetsTypesTableName} from '@tess-f/sql-tables/dist/evaluations/dynamic-question-sets-types.js'
import saveAssessment from './save-assessment-service.js'
import { SessionQuestionFields, SessionQuestionsTableName } from '@tess-f/sql-tables/dist/evaluations/session-questions.js'
import { SessionFields, SessionsTableName } from '@tess-f/sql-tables/dist/evaluations/session.js'
import { EvaluationTypes } from '@tess-f/sql-tables/dist/evaluations/evaluation-type.js'
import { QuestionTypes } from '@tess-f/sql-tables/dist/evaluations/question-type.js'

type OrderInfo = {
  /** Display index of the section itself within the assessment */
  SectionDisplayIndex: number
  /** Display index of the question or dynamic set within the section */
  DisplayIndexWithinSection: number
  /** Section ID */
  SectionId: string
  /** Section Version */
  SectionVersion: number
  /** Section Title */
  SectionTitle: string
}
/** A core question version with display order info but no options */
type OrderedQuestion = QuestionVersion & OrderInfo
/** A core dynamic question set filter definition with display order info but no questions */
type OrderedSet = DynamicQuestionSetVersion & OrderInfo
/* A dynamic question set with display order info and its contained questions but none of their options.
   The array order of the Questions array is the display order of the questions within the set. */
type OrderedSetWithQuestions = OrderedSet & { Questions: QuestionVersion[] }

// This is meant to capture the structure of an assessment where each section contains some number of
// questions and dynamic set containers.
type SectionContents = { indexWithinSection: number, question?: OrderedQuestion, set?: OrderedSetWithQuestions }[]

/**
 * Builds the SQL query to retrieve fixed questions for an evaluation
 */
async function getFixedQuestions(evaluation: CurrentEvaluationView): Promise<OrderedQuestion[]> {
  const fixedRequest = mssql.getPool().request()
  fixedRequest.input('evalId', evaluation.Id)
  fixedRequest.input('evalVersion', evaluation.Version)

  // Get QuestionVersions plus section & display order for all fixed questions in the evaluation.
  // These will be ordered in their design-time display order. If evaluation.RandomizeQuestions is true,
  // we will later scramble the order within each section.
  // These do not yet contain their options.
  const fixedQuestions = (await fixedRequest.query<OrderedQuestion>(`
    SELECT qv.*,
      sv.[${EvaluationSectionVersionFields.DisplayIndex}] as SectionDisplayIndex,
      eq.[${EvaluationQuestionFields.DisplayIndex}] as DisplayIndexWithinSection,
      sv.[${EvaluationSectionVersionFields.SectionId}] as SectionId,
      sv.[${EvaluationSectionVersionFields.Version}] as SectionVersion,
      sv.[${EvaluationSectionVersionFields.Title}] as SectionTitle
      FROM [${QuestionVersionViewName}] AS qv
      INNER JOIN [${EvaluationQuestionTableName}] AS eq ON eq.[${EvaluationQuestionFields.QuestionId}] = qv.[${QuestionVersionFields.Id}] AND eq.[${EvaluationQuestionFields.QuestionVersion}] = qv.[${QuestionVersionFields.Version}]
      INNER JOIN [${EvaluationSectionVersionTableName}] sv ON sv.[${EvaluationSectionVersionFields.SectionId}] = eq.[${EvaluationQuestionFields.SectionId}] AND sv.[${EvaluationSectionVersionFields.Version}] = eq.[${EvaluationQuestionFields.SectionVersion}]
      ORDER BY SectionDisplayIndex, DisplayIndexWithinSection
      `)).recordset

  return fixedQuestions
}

/**
 * Builds the SQL query to retrieve dynamic question set definitions for an evaluation
 */
async function getDynamicQuestionSets(evaluation: CurrentEvaluationView): Promise<OrderedSet[]> {
  const dynamicSetsRequest = mssql.getPool().request()
  dynamicSetsRequest.input('evalId', evaluation.Id)
  dynamicSetsRequest.input('evalVersion', evaluation.Version)

  // Get IDs of all dynamic question set definitions for the evaluation, ordered by their
  // design-time display order.
  // These are just the sets' filter definitions and not the questions themselves.
  const dynamicSets = (await dynamicSetsRequest.query<OrderedSet>(`
    SELECT setVersions.*,
      section.[${EvaluationSectionVersionFields.DisplayIndex}] AS SectionDisplayIndex,
      eq.[${EvaluationQuestionFields.DisplayIndex}] AS DisplayIndexWithinSection,
      section.[${EvaluationSectionVersionFields.SectionId}] as SectionId,
      section.[${EvaluationSectionVersionFields.Version}] as SectionVersion,
      section.[${EvaluationSectionVersionFields.Title}] as SectionTitle
	    FROM [${EvaluationQuestionTableName}] AS eq
    	INNER JOIN [${DynamicQuestionSetVersionTableName}] AS setVersions ON setVersions.[${DynamicQuestionSetVersionFields.QuestionSetId}] = eq.[${EvaluationQuestionFields.QuestionSetId}] AND setVersions.[${DynamicQuestionSetVersionFields.Version}] = eq.[${EvaluationQuestionFields.QuestionSetVersion}]
      INNER JOIN [${EvaluationSectionVersionTableName}] AS section ON section.[${EvaluationSectionVersionFields.SectionId}] = eq.[${EvaluationQuestionFields.SectionId}] AND section.[${EvaluationSectionVersionFields.Version}] = eq.[${EvaluationQuestionFields.SectionVersion}]
      WHERE eq.[${EvaluationQuestionFields.EvaluationId}] = @evalId AND eq.[${EvaluationQuestionFields.EvaluationVersion}] = @evalVersion
      ORDER BY SectionDisplayIndex, DisplayIndexWithinSection
  `)).recordset

  return dynamicSets
}

/**
 * Builds the SQL query to retrieve questions for a specific dynamic question set.
 * Questions are selected with preference for those the user has not seen before.
 */
async function getQuestionsForDynamicSet(
  set: OrderedSet,
  usedQuestionIds: string[],
  userId: string
): Promise<QuestionVersion[]> {
  const setRequest = mssql.getPool().request()
  setRequest.input('questionSetId', set.QuestionSetId)
  setRequest.input('questionSetVersion', set.Version)
  // SubsetSize is honored only if UseAll is false
  setRequest.input('subsetSize', set.SubsetSize)
  setRequest.input('userId', userId)

  // Get the usage-based ordering clause to prioritize questions the user hasn't seen

  // This will retrieve a selection of questions that belong to assessment-type question banks
  // and match the question bank, question type, and question objective filters.
  // The set size is restricted by SubsetSize if UseAll is false.
  // Any fixed questions or previously chosen dynamic questions are excluded from the set.
  // Questions are ordered to prioritize those the user has not seen in previous sessions.
  // It is possible to end up with less than SubsetSize questions or even zero questions if
  // they've all already been used in the assessment.
  // Note that UNION inherently ensures distinct results.
  const setQuestionsQuery = `
    WITH FilteredQuestions AS (
      -- Questions that match the question bank filter
      SELECT qVersions.*
        FROM [${DynamicQuestionSetsBanksTableName}] AS setBanks
        INNER JOIN [${QuestionBankQuestionsTableName}] AS bankQuestions ON bankQuestions.[${QuestionBankQuestionFields.QuestionBankId}] = setBanks.[${DynamicQuestionSetsBanksFields.QuestionBankId}] AND bankQuestions.[${QuestionBankQuestionFields.QuestionBankVersion}] = setBanks.[${DynamicQuestionSetsBanksFields.QuestionBankVersion}]
        INNER JOIN [${QuestionVersionViewName}] AS qVersions ON qVersions.[${QuestionVersionFields.Id}] = bankQuestions.[${QuestionBankQuestionFields.QuestionId}] AND qVersions.[${QuestionVersionFields.Version}] = bankQuestions.[${QuestionBankQuestionFields.QuestionVersion}]
        WHERE setBanks.[${DynamicQuestionSetsBanksFields.QuestionSetId}] = @questionSetId AND setBanks.[${DynamicQuestionSetsBanksFields.QuestionSetVersion}] = @questionSetVersion

      UNION

      -- Questions that match the question type filter
      SELECT qVersions.*
        FROM [${DynamicQuestionSetsTypesTableName}] AS setTypes
        INNER JOIN [${QuestionVersionViewName}]AS qVersions ON qVersions.[${QuestionVersionFields.QuestionTypeId}] = setTypes.[${DynamicQuestionSetsTypesFields.QuestionTypeId}]
        -- Join question banks so we can restrict ourselves to questions from assessment-type question banks only
        INNER JOIN [${QuestionBankQuestionsTableName}] AS qBankQs ON qBankQs.[${QuestionBankQuestionFields.QuestionId}] = qVersions.[${QuestionVersionFields.Id}] AND qBankQs.[${QuestionBankQuestionFields.QuestionVersion}] = qVersions.[${QuestionVersionFields.Version}]
        INNER JOIN [${QuestionBanksTableName}] AS qBanks ON qBanks.[${QuestionBankFields.Id}] = qBankQs.[${QuestionBankQuestionFields.QuestionBankId}]
        WHERE setTypes.[${DynamicQuestionSetsTypesFields.QuestionSetId}] = @questionSetId AND setTypes.[${DynamicQuestionSetsTypesFields.QuestionSetVersion}] = @questionSetVersion
          AND qBanks.[${QuestionBankFields.EvaluationTypeId}] = ${EvaluationTypes.Assessment} -- questions from assessment-type banks only

      UNION

      -- Questions that match the objectives filter
      SELECT qVersions.*
	        FROM [${DynamicQuestionSetsObjectivesTableName}] AS setObjs
        INNER JOIN [${QuestionsObjectivesTableName}] AS qObjs ON qObjs.[${QuestionObjectiveFields.ObjectiveId}] = setObjs.[${DynamicQuestionSetsObjectivesFields.ObjectiveId}]
        INNER JOIN [${QuestionVersionViewName}] AS qVersions ON qVersions.[${QuestionVersionFields.Id}] = qObjs.[${QuestionObjectiveFields.QuestionId}] AND qVersions.[${QuestionVersionFields.Version}] = qObjs.[${QuestionObjectiveFields.QuestionVersion}]
        -- Join question banks so we can restrict ourselves to questions from assessment-type question banks only
        INNER JOIN [${QuestionBankQuestionsTableName}] AS qBankQs ON qBankQs.[${QuestionBankQuestionFields.QuestionId}] = qVersions.[${QuestionVersionFields.Id}] AND qBankQs.[${QuestionBankQuestionFields.QuestionVersion}] = qVersions.[${QuestionVersionFields.Version}]
        INNER JOIN [${QuestionBanksTableName}] AS qBanks ON qBanks.[${QuestionBankFields.Id}] = qBankQs.[${QuestionBankQuestionFields.QuestionBankId}]
        WHERE setObjs.[${DynamicQuestionSetsObjectivesFields.QuestionSetId}] = @questionSetId AND setObjs.[${DynamicQuestionSetsObjectivesFields.QuestionSetVersion}] = @questionSetVersion
          AND qBanks.EvaluationTypeId = ${EvaluationTypes.Assessment} -- questions from assessment-type banks only
    )

    SELECT
      -- Limit # of chosen questions if UseAll is false
      ${set.UseAll ? '' : `TOP ${set.SubsetSize}`}
      fq.*,
      [qusetionUse].[UsageCount]
      FROM FilteredQuestions AS fq
        OUTER APPLY (
          SELECT COUNT(*) AS UsageCount
          FROM [${SessionQuestionsTableName}] AS sq
          INNER JOIN [${SessionsTableName}] AS s 
            ON s.[${SessionFields.Id}] = sq.[${SessionQuestionFields.SessionId}] AND s.[${SessionFields.UserId}] = @userId
          WHERE sq.[${SessionQuestionFields.QuestionId}] = fq.[${QuestionVersionFields.Id}]
        ) AS [qusetionUse]
      -- Exclude questions that are already used in the evaluation
      ${usedQuestionIds.length > 0 ? `WHERE fq.[${QuestionVersionFields.Id}] NOT IN ('${usedQuestionIds.join('\',\'')}')` : ''}
      -- Order by usage count (prefer unseen questions), then randomize within same usage count:
      ORDER BY [qusetionUse].[UsageCount] ASC
  `

  // Get selected questions for this dynamic question set from the database.
  // We'll use the order they appear in this array as the order to present them in within
  // this dynamic set in the assessment.
  const setQuestions = (await setRequest.query<QuestionVersion>(setQuestionsQuery)).recordset
  return setQuestions
}

/**
 * Creates the section structure and adds fixed questions to it
 */
function createSectionStructure(
  fixedQuestions: OrderedQuestion[],
  dynamicSets: OrderedSet[]
): Map<number, SectionContents> {
  // Build a Set containing all unique section display indexes of all fixed questions and all dynamic sets
  const uniqueSectionIndexes: Set<number> = new Set(
    fixedQuestions.map(q => q.SectionDisplayIndex).concat(dynamicSets.map(s => s.SectionDisplayIndex))
  )

  // This is a Map of sections (by display index) where each entry contains an array of questions and/or sets.
  const combinedListBySection = new Map<number, SectionContents>(
    Array.from(uniqueSectionIndexes).map(sectionIndex => [sectionIndex, []])
  )

  for (const fixedQuestion of fixedQuestions) {
    combinedListBySection.get(fixedQuestion.SectionDisplayIndex)?.push({
      indexWithinSection: fixedQuestion.DisplayIndexWithinSection,
      question: fixedQuestion
    })
  }

  return combinedListBySection
}

/**
 * Randomizes the order of fixed questions and dynamic set containers within each section
 */
function randomizeSectionOrdering(
  combinedListBySection: Map<number, SectionContents>
): void {
  for (const section of combinedListBySection.values()) {
    section.sort(() => Math.random() - 0.5)
  }
}

/**
 * Sorts the order of fixed questions and dynamic set containers within each section based on their design-time positions
 */
function applySectionOrderingByDesignTime(
  combinedListBySection: Map<number, SectionContents>
): void {
  for (const section of combinedListBySection.values()) {
    section.sort((a, b) => a.indexWithinSection - b.indexWithinSection)
  }
}

/**
 * Processes all dynamic question sets and adds them to the section structure
 */
async function processDynamicQuestionSets(
  dynamicSets: OrderedSet[],
  combinedListBySection: Map<number, SectionContents>,
  usedQuestionIds: string[],
  userId: string
): Promise<void> {
  // For each dynamic question set, get its questions without repeating any
  // fixed or previously drawn random dynamic set questions
  for (const set of dynamicSets) {
    // Get selected questions for this dynamic question set from the database.
    // Questions are prioritized to avoid reuse from previous sessions when possible.
    // We'll use the order they appear in this array as the order to present them in within
    // this dynamic set in the assessment.
    const setQuestions = await getQuestionsForDynamicSet(set, usedQuestionIds, userId)

    // Inject these questions into the dynamic set container
    const setWithQuestions: OrderedSetWithQuestions = {...set, Questions: setQuestions}

    // Add this set (containing its questions) to the master combined list of fixed questions and dynamic sets
    combinedListBySection.get(set.SectionDisplayIndex)?.push({
      indexWithinSection: set.DisplayIndexWithinSection,
      set: setWithQuestions
    })

    // Add these questions IDs to our list of used IDs
    usedQuestionIds.push(...setQuestions.map(q => q.Id!))
  }
}

/**
 * Builds the final ordered list of questions with their options, organized by section
 */
async function buildFinalQuestionList(
  combinedListBySection: Map<number, SectionContents>
): Promise<EvaluationSectionWithQuestions[]> {
  // Build sections with their questions in order
  const sectionsWithQuestions: EvaluationSectionWithQuestions[] = []

  // Process sections in order by their section display index
  const sortedSectionKeys = Array.from(combinedListBySection.keys()).sort((a, b) => a - b)
  for (const sectionIndex of sortedSectionKeys) {
    const section = combinedListBySection.get(sectionIndex)!

    // Get section metadata from the first question or set in the section
    const firstItem = section[0]
    const sectionMetadata = firstItem.question ?? firstItem.set!

    const sectionQuestions: QuestionWithOptions[] = []

    // Each item in the section is either a fixed question or a dynamic set container
    for (const questionOrSet of section) {
      if (questionOrSet.question) {
        // Add DisplayIndex for test compatibility
        const questionWithDisplayIndex = {
          ...questionOrSet.question,
          DisplayIndex: questionOrSet.question.DisplayIndexWithinSection
        }
        const questionWithOptions: QuestionWithOptions = await buildQuestionWithOptions(questionWithDisplayIndex)
        sectionQuestions.push(questionWithOptions)
      } else { // it's a dynamic set
        for (const dynamicQuestion of questionOrSet.set!.Questions) {
          // Add DisplayIndex, SectionDisplayIndex, QuestionSetId, and QuestionSetVersion to dynamic questions
          const orderedDynamicQuestion: OrderedQuestion & { DisplayIndex: number } = {
            ...dynamicQuestion,
            DisplayIndexWithinSection: questionOrSet.set!.DisplayIndexWithinSection,
            SectionDisplayIndex: questionOrSet.set!.SectionDisplayIndex,
            SectionId: questionOrSet.set!.SectionId,
            SectionVersion: questionOrSet.set!.SectionVersion,
            SectionTitle: questionOrSet.set!.SectionTitle,
            DisplayIndex: questionOrSet.set!.DisplayIndexWithinSection  // For test compatibility
          }
          const questionWithOptions: QuestionWithOptions = await buildQuestionWithOptions(orderedDynamicQuestion)
          sectionQuestions.push(questionWithOptions)
        }
      }
    }

    // Create the section with its questions
    sectionsWithQuestions.push({
      SectionId: sectionMetadata.SectionId,
      Version: sectionMetadata.SectionVersion,
      Title: sectionMetadata.SectionTitle,
      DisplayIndex: sectionMetadata.SectionDisplayIndex,
      Questions: sectionQuestions
    })
  }

  return sectionsWithQuestions
}

export default async function assessmentGenerator (userId: string, evaluation: CurrentEvaluationView, sessionId: string): Promise<EvaluationSectionWithQuestions[]> {
  // 1. Get fixed questions w/ section display order
  // 2. Get dynamic set definitions w/ section display order
  // 3. Get random dynamic questions for each set (w/ section display order). Iterate through each set and get
  //    from assessment question banks (bank type 1) per each set's filters and size requirements.
  //    This is selection of questions is always randomized. (This is independent of eval.RandomizeQuestions (which is
  //    misnamed and should be called eval.ScrambleQuestions) which "scrambles" (randomizes) the order of
  //    fixed questions and sets within a section, but has no bearing on the questions within a set.)
  //    Each set much exclude all fixed questions and any prior chosen dynamic questions so that no question
  //    appears twice within the assessment. It's possible to end up with fewer questions than you asked for.
  // 4. Assign final absolute display order of all fixed and dynamic questions (the absolute display index of each
  //    question within the presented assessment).
  //    If eval.RandomizeQuestions is false, use each fixed question and dynamic set's design-time positioning
  //    If eval.RandomizeQuestions is true (meaning the order of questions should be scrambled), questions and dynamic sets
  //    must stay within their assigned section when their order is scrambled. Dynamic questions do not need to be
  //    scrambled within their set because their order has already been randomized at the time of retrieval.
  //    Dynamic questions within a set must be constrained within the set and will appear in the order they were randomly
  //    pulled from the database (think of the sets the same way as sections but a level deeper --
  //    at this point, sets are basically subsections within their primary sections as far as ordering is concerned).
  // 5. Turn all questions into QuestionWithOptions by retrieving their option data.
  // 6. Determine display order of options within each question.
  //    If question.ScrambleOptions is false, honor each option.OrderId as defined in the database.
  //    If question.ScrambleOptions is true, randomize the order of the options with the question.
  //    However, this is complicated by the option.Locked flags, which means you must honor/retain
  //    the original option.OrderId while randomizing any other unlocked options around the locked options.

  // ---------- Get fixed questions ----------
  const fixedQuestions = await getFixedQuestions(evaluation)

  // A list of all fixed and dynamic question IDs that will grow as we pick questions for dynamic question sets.
  // Initialize with all fixed question IDs.
  const usedQuestionIds: string[] = fixedQuestions.map(question => question.Id!)

  // ---------- Get dynamic question set filter definitions ----------
  const dynamicSets = await getDynamicQuestionSets(evaluation)

  // ---------- Create section structure and add fixed questions ----------
  const combinedListBySection = createSectionStructure(fixedQuestions, dynamicSets)

  // ---------- Process dynamic question sets ----------
  // Dynamic pools will try to avoid showing questions the user has seen in prior assessments
  await processDynamicQuestionSets(dynamicSets, combinedListBySection, usedQuestionIds, userId)

  // ---------- Apply final ordering ----------
  if (evaluation.RandomizeQuestions ?? false) {
    randomizeSectionOrdering(combinedListBySection)
  } else {
    applySectionOrderingByDesignTime(combinedListBySection)
  }

  // ---------- Build final question list with options ----------
  const sectionsWithQuestions = await buildFinalQuestionList(combinedListBySection)

  // Flatten sections to get all questions in order for saving to the database
  const flattenedQuestions: QuestionWithOptions[] = sectionsWithQuestions.flatMap(section => section.Questions)

  // Save assessment before moving forward
  await saveAssessment({evalId: evaluation.Id!, evalVersion: evaluation.Version!, userId: userId, questions: flattenedQuestions, sessionId: sessionId})

  // Return sections with their questions in presentation order
  return sectionsWithQuestions
}

/**
 * Determines if a question type ID represents a match question
 * @param questionTypeId The question type ID to check
 * @returns True if this is a match question type
 */
function isMatchQuestion(questionTypeId: number): boolean {
  try {
    return questionTypeId === QuestionTypes.Matching
  } catch {
    // If QuestionTypes doesn't have this properties, fall back to false
    return false
  }
}

/**
 * Scrambles options for match type questions with special handling for source/target pairs
 * Only applies special logic if IsTarget property is available, otherwise returns null to use normal scrambling
 * @param options The options to scramble
 * @returns The scrambled options array, or null if special match logic cannot be applied
 */
function scrambleMatchOptions(options: OptionVersion[]): OptionVersion[] | null {
  if (options.length === 0) {
    return options
  }

  // For match questions, options come in pairs where one is source and one is target
  // - Target side should not sort but source side should
  // - If target is false, it should sort/scramble as it is a source

  // Check for IsTarget property to identify source/target options
  const hasIsTargetProperty = options.some(opt => 'IsTarget' in opt)

  if (!hasIsTargetProperty) {
    // No IsTarget property available - cannot apply special match logic
    // Return null to indicate normal scrambling should be used
    return null
  }

  // Use IsTarget property - if not target, then it's source
  const targetOptions = options.filter(opt => opt.IsTarget === true)
  const sourceOptions = options.filter(opt => opt.IsTarget !== true)

  // Scramble source options (these should be randomized)
  const scrambledSourceOptions = sourceOptions.toSorted(() => Math.random() - 0.5)
  for (const [index, option] of scrambledSourceOptions.entries()) {
    option.OrderId = index + 1
  }

  // Keep target options in their original order (these should not be scrambled)
  const sortedTargetOptions = targetOptions.toSorted((a, b) => (a.OrderId ?? 0) - (b.OrderId ?? 0))

  // Combine back together - typically source options come first, then target options
  // But we maintain the original pattern
  const result: OptionVersion[] = []
  let sourceIndex = 0
  let targetIndex = 0

  // Rebuild maintaining the original source/target pattern
  for (const originalOption of options) {
    const isSourceOption = sourceOptions.includes(originalOption)
    if (isSourceOption) {
      result.push(scrambledSourceOptions[sourceIndex++])
    } else {
      result.push(sortedTargetOptions[targetIndex++])
    }
  }

  return result
}

/**
 * Scrambles options for a question while respecting locked options.
 * Locked options remain in their original positions (by OrderId).
 * Unlocked options are randomized around the locked options.
 * For match type questions, handles source/target separation properly.
 * @param options The options to scramble
 * @param questionTypeId The question type ID to determine scrambling behavior
 * @returns The scrambled options array
 */
function scrambleOptions(options: OptionVersion[], questionTypeId?: number): OptionVersion[] {
  if (options.length === 0) {
    return options
  }

  // Sort options by their original OrderId to establish baseline order
  const sortedOptions = [...options].toSorted((a, b) => (a.OrderId ?? 0) - (b.OrderId ?? 0))

  // Handle match type questions specially
  // Note: Using a more generic check since we need to determine the exact QuestionTypes property name
  // This will be updated once we know the correct property name for match questions
  if (questionTypeId && isMatchQuestion(questionTypeId)) {
    const matchResult = scrambleMatchOptions(sortedOptions)
    if (matchResult !== null) {
      return matchResult
    }
    // If match logic couldn't be applied (no IsTarget property), fall through to normal scrambling
  }

  // Separate locked and unlocked options
  const lockedOptions = sortedOptions.filter(opt => opt.Locked === true)
  const shuffledUnlocked = sortedOptions.filter(opt => opt.Locked !== true).toSorted(() => Math.random() - 0.5)

  // If all options are locked or unlocked, handle simply
  if (lockedOptions.length === 0) {
    for (const option of shuffledUnlocked) {
      option.OrderId = sortedOptions.indexOf(option) + 1
    }

    return shuffledUnlocked
  }

  if (shuffledUnlocked.length === 0) {
    // Keep all locked options in their original order
    return lockedOptions
  }

  // Build the final array by placing locked options at their original positions
  // and filling in unlocked options in the remaining positions
  const result: OptionVersion[] = []
  let unlockedIndex = 0
  let orderIndex = 0
  for (const originalOption of sortedOptions) {
    if (originalOption.Locked === true) {
      // Place locked option at its original position
      result.push(originalOption);
    } else {
      // Place next shuffled unlocked option
      result.push(shuffledUnlocked[unlockedIndex++]);
    }
    //reindex the order id to ensure client and server match
    if (result.length > 0) {
      const lastItem = result.at(-1) as NonNullable<typeof result[number]>;
      lastItem.OrderId = orderIndex++;
    }

  }
  
  return result
}

async function buildQuestionWithOptions(
  question: QuestionVersion | OrderedQuestion
): Promise<QuestionWithOptions> {
  const subQuestions = await getChildrenForQuestion(question.Id!, question.Version!)

  // Get options for the main question
  let options = await getOptionsForQuestion(question.Id!, question.Version!)

  // Scramble options if ScrambleOptions is true, respecting locked options
  if (question.ScrambleOptions === true) {
    options = scrambleOptions(options, question.QuestionTypeId)
  } else {
    // If not scrambling, sort by OrderId to maintain design-time order
    options = [...options].sort((a, b) => (a.OrderId ?? 0) - (b.OrderId ?? 0))
  }

  // Process sub-questions
  const subQuestionsWithOptions = await Promise.all(subQuestions.map(async subQuestion => {
    let subOptions = await getOptionsForQuestion(subQuestion.Id ?? '', subQuestion.Version ?? 1)

    // Scramble sub-question options if ScrambleOptions is true
    if (subQuestion.ScrambleOptions === true) {
      subOptions = scrambleOptions(subOptions, subQuestion.QuestionTypeId)
    } else {
      // If not scrambling, sort by OrderId to maintain design-time order
      subOptions = [...subOptions].sort((a, b) => (a.OrderId ?? 0) - (b.OrderId ?? 0))
    }

    return {
      ...subQuestion,
      Options: subOptions
    }
  }))

  return {
    ...question,
    Options: options,
    SubQuestions: subQuestionsWithOptions
  }
}