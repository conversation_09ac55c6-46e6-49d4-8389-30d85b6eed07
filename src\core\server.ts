import logger from '@lcs/logger'
import express from 'express'
import settings from '../config/settings.js'
import router from './router.js'
import sessionAuthority from '@lcs/session-authority'
import mssql from '@lcs/mssql-utility'
import { getErrorMessage } from '@tess-f/backend-utils'
import { RedisClient } from '../services/redis/client.service.js'
import rabbitmq from '@lcs/rabbitmq'
import { EventType } from '@tess-f/shared-config/dist/tess-config/logger.js'

const log = logger.create('Server')
const sessionLog = logger.create('Server.Session-Authority')

export default async function main (): Promise<void> {
  const app = express()
  // remove the discloser of the apps fingerprinting
  app.disable('x-powered-by')

  try {
    settings.print()
  } catch (error) {
    console.error('Could not print settings', { error })
  }

  try {
    // set up logger
    setupLogger()

    // set up session authority
    await setupSessionAuthority()

    // set up mssql
    await setupMssql()

    // set up rabbit mq
    await setupRabbit()

    // set up app routing
    app.use(settings.server.basePath, router)

    // Start the server
    const server = app.listen(settings.server.port, () => {
      console.log(`listening on port :: ${settings.server.port}`)
    })

    server.setTimeout(settings.server.timeout)
  } catch (error) {
    await handleFatalError(error)
  }
}

async function handleFatalError (error: unknown): Promise<void> {
  // shutdown any running services or open connections
  RedisClient.shutdown()
  console.error('Critical server error', error)
  process.exit(-1)
}

async function setupMssql (): Promise<void> {
  await mssql.init(settings.mssql.connectionConfig, settings.mssql.forceEncrypted, settings.mssql.streamChunkSize)
  log('info', 'MSSQL connection initialized', { database: settings.mssql.connectionConfig.database, server: settings.mssql.connectionConfig.server, success: true, eventType: EventType.server_startup })
}

async function setupRabbit (): Promise<void> {
  // sanitize with minimums
  const delay = settings.amqp.config.reconnectDelayMilli > 0 ? settings.amqp.config.reconnectDelayMilli : 15000
  const attempts = settings.amqp.config.reconnectMaxAttempts > 0 ? settings.amqp.config.reconnectMaxAttempts : 30

  rabbitmq.on('reconnect', async () => {
    await rabbitmq.reconnectRabbit(settings.amqp.config, delay, attempts)
  })

  await rabbitmq.connect(settings.amqp.config)
  log('info', 'RabbitMQ connection initialized', { success: true, eventType: EventType.server_startup })
}

function setupLogger (): void {
  const loggerConfig = JSON.parse(JSON.stringify(settings.logger))
  if (loggerConfig.useElasticsearch === false) {
    // if we are not using elasticsearch delete the options
    delete loggerConfig.elasticsearch
  }
  logger.init(loggerConfig)
  log('info', 'Logger initialized', { success: true, eventType: EventType.server_startup })
}

async function setupSessionAuthority (): Promise<void> {
  if (settings.sessionAuthority === undefined) {
    throw new Error('Failed to set up session authority missing config')
  }

  sessionAuthority.events.on('verbose', message => {
 sessionLog('verbose', message.message, { data: message.data, eventType: EventType.session_authority_logs }) 
})
  sessionAuthority.events.on('info', message => {
 sessionLog('info', message.message, { data: message.data, eventType: EventType.session_authority_logs }) 
})
  sessionAuthority.events.on('error', message => {
 sessionLog('error', message.message, { errorMessage: getErrorMessage(message.error) }) 
})

  await sessionAuthority.init(settings.sessionAuthority)
  log('info', 'Session authority initialized', { success: true, eventType: EventType.server_startup })
}
