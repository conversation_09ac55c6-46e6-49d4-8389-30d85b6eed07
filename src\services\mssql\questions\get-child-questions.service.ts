import mssql, { DB_Errors as dbErrors, getRows } from '@lcs/mssql-utility'
import { getErrorMessage } from '@tess-f/backend-utils'
import { type QuestionVersion, QuestionVersionViewName } from '@tess-f/sql-tables/dist/evaluations/question-version-view.js'

export default async function getChildrenForQuestion (parentQuestionId: string, parentVersion: number): Promise<QuestionVersion[]> {
  try {
    return await getRows<QuestionVersion>(QuestionVersionViewName, mssql.getPool().request(), { ParentId: parentQuestionId, ParentVersion: parentVersion })
  } catch (error) {
    if (getErrorMessage(error) === dbErrors.default.NOT_FOUND_IN_DB) {
      return []
    }
    throw error
  }
}
