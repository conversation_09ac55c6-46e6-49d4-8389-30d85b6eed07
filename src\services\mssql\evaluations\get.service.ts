import mssql, { getRows } from '@lcs/mssql-utility'
import { type CurrentEvaluationView, CurrentEvaluationViewName } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'

export default async function getEvaluation (evaluationId: string): Promise<CurrentEvaluationView> {
  const records = await getRows<CurrentEvaluationView>(CurrentEvaluationViewName, mssql.getPool().request(), { Id: evaluationId })
  return records[0]
}
