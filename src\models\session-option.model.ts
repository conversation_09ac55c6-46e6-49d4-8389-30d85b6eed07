import { Table } from '@lcs/mssql-utility'
import { EvalSessionOption, SessionOptionFields, SessionOptionsTableName } from '@tess-f/sql-tables/dist/evaluations/session-options.js'

export type { EvalSessionOption }

export class SessionOptionModel extends Table<EvalSessionOption, EvalSessionOption> {
  fields: EvalSessionOption

  constructor (fields?: EvalSessionOption, record?: EvalSessionOption) {
    super(
      SessionOptionsTableName,
      [
        SessionOptionFields.SessionId,
        SessionOptionFields.QuestionId,
        SessionOptionFields.QuestionVersion,
        SessionOptionFields.OptionId,
        SessionOptionFields.OptionVersion
      ]
    )

    this.fields = fields ?? {}
    if (record) this.importFromDatabase(record)
  }

  importFromDatabase (record: EvalSessionOption): void {
    this.fields = {
      SessionId: record.SessionId,
      QuestionId: record.QuestionId,
      QuestionVersion: record.QuestionVersion,
      OptionId: record.OptionId,
      OptionVersion: record.OptionVersion,
      PresentationIndex: record.PresentationIndex
    }
  }

  exportJsonToDatabase (): EvalSessionOption {
    return {
      SessionId: this.fields.SessionId,
      QuestionId: this.fields.QuestionId,
      QuestionVersion: this.fields.QuestionVersion,
      OptionId: this.fields.OptionId,
      OptionVersion: this.fields.OptionVersion,
      PresentationIndex: this.fields.PresentationIndex
    }
  }
}
