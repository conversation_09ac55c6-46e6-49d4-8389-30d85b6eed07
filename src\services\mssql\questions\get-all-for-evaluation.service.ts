import mssql from '@lcs/mssql-utility'
import { type QuestionVersion, QuestionVersionFields, QuestionVersionViewName } from '@tess-f/sql-tables/dist/evaluations/question-version-view.js'
import { EvaluationQuestionBanksTableName, EvaluationQuestionBankFields } from '@tess-f/sql-tables/dist/evaluations/evaluation-question-bank.js'
import { QuestionBankQuestionFields, QuestionBankQuestionsTableName } from '@tess-f/sql-tables/dist/evaluations/question-bank-question.js'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import getChildrenForQuestion from './get-child-questions.service.js'
import getOptionsForQuestion from '../question-options/get-for-question.service.js'

export default async function getAllQuestionsForEvaluation (evalId: string, version: number): Promise<QuestionWithOptions[]> {
  const request = mssql.getPool().request()

  request.input('evalId', evalId)
  request.input('evalVersion', version)

  const results = await request.query<QuestionVersion>(`
    SELECT [${QuestionVersionViewName}].*
    FROM [${QuestionVersionViewName}]
      JOIN [${QuestionBankQuestionsTableName}] ON
        [${QuestionBankQuestionsTableName}].[${QuestionBankQuestionFields.QuestionId}] = [${QuestionVersionViewName}].[${QuestionVersionFields.Id}]
        AND [${QuestionBankQuestionsTableName}].[${QuestionBankQuestionFields.QuestionVersion}] = [${QuestionVersionViewName}].[${QuestionVersionFields.Version}]
      JOIN [${EvaluationQuestionBanksTableName}] ON
        [${EvaluationQuestionBanksTableName}].[${EvaluationQuestionBankFields.QuestionBankId}] = [${QuestionBankQuestionsTableName}].[${QuestionBankQuestionFields.QuestionBankId}]
        AND [${EvaluationQuestionBanksTableName}].[${EvaluationQuestionBankFields.QuestionBankVersion}] = [${QuestionBankQuestionsTableName}].[${QuestionBankQuestionFields.QuestionBankVersion}]
        AND [${EvaluationQuestionBanksTableName}].[${EvaluationQuestionBankFields.EvaluationId}] = @evalId
        AND [${EvaluationQuestionBanksTableName}].[${EvaluationQuestionBankFields.EvaluationVersion}] = @evalVersion
  `)

  return await Promise.all(results.recordset.map(async question => {
    // get any sub questions this question may have (likerts and fill in the blanks use nested questions)
    const subQuestions = await getChildrenForQuestion(question.Id ?? '', question.Version ?? 1)

    return {
      ...question,
      Options: await getOptionsForQuestion(question.Id ?? '', question.Version ?? 1), // get the answer options
      SubQuestions: await Promise.all(subQuestions.map(async subQuestion => {
        return {
          ...subQuestion,
          Options: await getOptionsForQuestion(subQuestion.Id ?? '', subQuestion.Version ?? 1) // get the answer options
        }
      }))
    }
  }))
}
