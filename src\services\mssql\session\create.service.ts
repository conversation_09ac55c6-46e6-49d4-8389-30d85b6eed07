import mssql, { addRow } from '@lcs/mssql-utility'
import { SessionModel } from '../../../models/session.model.js'
import { EvaluationSession } from '@tess-f/sql-tables/dist/evaluations/session.js'

export default async function createEvaluationSession (session: SessionModel): Promise<SessionModel> {
  const record = await addRow<EvaluationSession>(mssql.getPool().request(), session)
  session.importFromDatabase(record)
  return session
}
