import mssql from '@lcs/mssql-utility'
import { type QuestionVersion, QuestionVersionViewName, QuestionVersionFields } from '@tess-f/sql-tables/dist/evaluations/question-version-view.js'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import getOptionsForQuestion from '../question-options/get-for-question.service.js'
import getChildrenForQuestion from './get-child-questions.service.js'

/**
 * Get a question with its options and sub-questions by questionId and version
 * 
 * @param questionId The question ID
 * @param version The question version
 * @returns The question with options, or null if not found
 */
export default async function getQuestionWithOptions(
  questionId: string,
  version: number
): Promise<QuestionWithOptions | null> {
  const request = mssql.getPool().request()
  request.input('questionId', questionId)
  request.input('version', version)

  const results = await request.query<QuestionVersion>(`
    SELECT * FROM [${QuestionVersionViewName}]
    WHERE [${QuestionVersionFields.Id}] = @questionId 
      AND [${QuestionVersionFields.Version}] = @version
  `)

  if (results.recordset.length === 0) {
    return null
  }

  const question = results.recordset[0]

  // Get options for this question
  const options = await getOptionsForQuestion(questionId, version)

  // Get any sub-questions (for likerts and fill in the blanks)
  const subQuestions = await getChildrenForQuestion(questionId, version)

  // Build the QuestionWithOptions object
  const questionWithOptions: QuestionWithOptions = {
    ...question,
    Options: options,
    SubQuestions: await Promise.all(subQuestions.map(async subQuestion => {
      return {
        ...subQuestion,
        Options: await getOptionsForQuestion(subQuestion.Id ?? '', subQuestion.Version ?? 1)
      }
    }))
  }

  return questionWithOptions
}

