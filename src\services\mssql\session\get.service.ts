import mssql, { getRows } from '@lcs/mssql-utility'
import { SessionModel } from '../../../models/session.model.js'
import { type EvaluationSession, SessionsTableName } from '@tess-f/sql-tables/dist/evaluations/session.js'
import { SessionQuestionsTableName } from '@tess-f/sql-tables/dist/evaluations/session-questions.js'

/**
 * Get an evaluation session by its ID
 */
export default async function getEvaluationSession (sessionId: string): Promise<SessionModel> {
  const records = await getRows<EvaluationSession>(SessionsTableName, mssql.getPool().request(), { Id: sessionId })
  return new SessionModel(undefined, records[0])
}

/**
 * Check if a session has assessment data (questions and options saved)
 */
export async function sessionHasAssessmentData (sessionId: string): Promise<boolean> {
  const request = mssql.getPool().request()
  request.input('sessionId', sessionId)

  const result = await request.query<{ Count: number }>(`
    SELECT COUNT(*) as Count
    FROM [${SessionQuestionsTableName}]
    WHERE SessionId = @sessionId
  `)

  return result.recordset[0]?.Count > 0
}
