import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import type { PaginatedItems } from '../../../models/internal/paginated-items.js'
import { type  CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'

describe('HTTP search controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./search.controller', {
            '../../../services/mssql/evaluations/search.service.js': {
                default: Sinon.stub().returns(Promise.resolve({
                    totalRecords: 0,
                    items: []
                } as unknown as PaginatedItems<CurrentEvaluationView>))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                createdAfter: '2021-01-01',
                createdBefore: '2021-01-01',
                createdByIds: [uuid()],
                evaluationTypeIds: [1, 2, 3],
                modifiedAfter: '2021-01-01',
                modifiedBefore: '2021-01-01',
                modifiedByIds: [uuid()],
                search: 'test',
                sortBy: 'modifiedOn',
                sortDirection: 'desc',
                limit: 10,
                offset: 0,
                systemId: 'test'
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./search.controller', {
            '../../../services/mssql/evaluations/search.service.js': {
                default: Sinon.stub().returns(Promise.resolve({
                    totalRecords: 0,
                    items: []
                } as unknown as PaginatedItems<CurrentEvaluationView>))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                search: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid data')
        expect(data).to.include('search')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./search.controller', {
            '../../../services/mssql/evaluations/search.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({
                    totalRecords: 0,
                    items: []
                } as unknown as PaginatedItems<CurrentEvaluationView>))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                createdAfter: '2021-01-01',
                createdBefore: '2021-01-01',
                createdByIds: [uuid()],
                evaluationTypeIds: [1, 2, 3],
                modifiedAfter: '2021-01-01',
                modifiedBefore: '2021-01-01',
                modifiedByIds: [uuid()],
                search: 'test',
                sortBy: 'modifiedOn',
                sortDirection: 'desc',
                limit: 10,
                offset: 0,
                systemId: 'test'
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})