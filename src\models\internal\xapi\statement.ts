import { Activity } from './activity.js'
import { Agent } from './agent.js'
import { StatementRef } from './statement-ref.js'
import { Verb } from './verb.js'

export interface Statement {
  id?: string
  actor: Agent
  verb: Verb
  object: Activity | Agent | StatementRef
  result?: {
    score?: {
      scaled?: number
      raw?: number
      min?: number
      max?: number
    },
    success?: boolean
    completion?: boolean
    response?: string
    duration?: string
    extensions?: Record<string, unknown>
  },
  context?: {
    registration?: string
    instructor?: Agent
    team?: Agent
    contextActivities?: {
      parent?: Activity[]
      grouping?: Activity[]
      category?: Activity[]
      other?: Activity[]
    }
    revision?: string
    platform?: string
    language?: string
    statement?: {
      id: string
      objectType: string
    }
    extensions?: Record<string, unknown>
  }
  timestamp?: string
  authority: Agent
}