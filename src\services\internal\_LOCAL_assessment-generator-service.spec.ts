import { assert, expect } from 'chai'
import logger from '@lcs/logger'
import mssql from '@lcs/mssql-utility'
import { CurrentEvaluationView } from '@tess-f/sql-tables/dist/evaluations/current-evaluation-view.js'
import assessmentGenerator from './assessment-generator-service.js'
import settings from '../../config/settings.js'

xdescribe('Assessment Generator Service', () => {
  before(async () => {
    logger.init({ level: 'silly' })
    await mssql.init(settings.mssql.connectionConfig, settings.mssql.forceEncrypted, settings.mssql.streamChunkSize)

  })

  // TODO this is a hardcoded mock that expects this assessment to exist in the database.
  // This is for local developer testing only and cannot be part of the "real" codebase.
  const evaluation: CurrentEvaluationView = {
    Id: '9CFB423E-F36B-1410-8025-004AC39F4610',
    Version: 1,
    EvaluationTypeId: 1,
    RandomizeQuestions: true
  } as CurrentEvaluationView
  const userId = '2DFA423E-F36B-1410-8025-004AC39F4610' //replace with valid user ID
  const sessionId = '2DFA423E-F36B-1410-8025-004AC39F4610'
  xit('generates an assessment', async () => {
    try {
      const result = await assessmentGenerator(userId, evaluation, sessionId)

      expect(result).to.have.length.greaterThanOrEqual(0)
    } catch (error) {
      console.log(error)
      assert(false, 'Failed to generate assessment')
    }
  })

})