import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import saveQuestionScore from '../../mssql/session-question-scores/save-question-score.service.js'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'
import createQuestionResponse from '../../mssql/question-response/create.service.js'
import { EventType } from '@tess-f/shared-config'

const log = logger.create('Service-internal.grade-text-question')
/**
 * Grade text questions
 * Used as sub-questions for fill-in-the-blank. Compares user's text input 
 * against all option text fields to find matches.
 * 
 * @param question The question with options
 * @param userResponses The user's responses
 * @param sessionId
 * @returns Grading result with correctness and points earned
 */
export default async function gradeText(
  question: QuestionWithOptions,
  userResponses: QuestionResponseModel[],
  sessionId: string
): Promise<SessionQuestionScoreModel> {
  // No response means incorrect
  if (userResponses.length === 0) {
    log('info', 'Cannot grade question, no user response. Score set to 0', {  questionId: question.Id, sessionId, success: true, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Pending: false,
      PartiallyCorrect: false,
      Correct: false
    }))
  }
  
  // For text questions, there should only be one response with text input
  const response = userResponses[0]
  if (!response.fields.ResponseText || question.Options.length === 0) {
    log('warn', 'User response missing or has no response text. Setting score to 0', { success: true, questionId: question.Id, sessionId, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Pending: false,
      PartiallyCorrect: false,
      Correct: false
    }))
  }

  // TODO: we need to address case sensitivity and punctuation settings
  // Compare user input against all option text fields (case-insensitive)
  const userText = response.fields.ResponseText.trim().toLowerCase()
  const isCorrect = question.Options.some(option => 
    option.Text && option.Text.trim().toLowerCase() === userText
  )
  
  // save the users response and score the question
  response.fields.Correct = isCorrect
  await createQuestionResponse(response)
  log('info', 'Successfully graded question', { questionId: question.Id, sessionId, correct: isCorrect, success: true, eventType: EventType.question_grade })
  return await saveQuestionScore(new SessionQuestionScoreModel({
    QuestionId: question.Id,
    QuestionVersion: question.Version,
    SessionId: sessionId,
    Correct: isCorrect,
    PartiallyCorrect: false,
    Pending: false,
    Score: isCorrect ? question.Points ?? 1 : 0
  }))
}