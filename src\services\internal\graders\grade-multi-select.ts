import logger from '@lcs/logger'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import saveQuestionScore from '../../mssql/session-question-scores/save-question-score.service.js'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'
import createQuestionResponse from '../../mssql/question-response/create.service.js'
import { EventType } from '@tess-f/shared-config'

const log = logger.create('Service-Internal.Grade-Multi-Select')

/**
 * Grades a question that has only one right answer (multiple choice or drop down selection)
 * @param question the question to grade
 * @param userResponses the users response to the question (should only have one response)
 * @param sessionId the id of the session this response is tied to
 * @returns 
 */
export default async function gradeMultiSelect(
  question: QuestionWithOptions,
  userResponses: QuestionResponseModel[],
  sessionId: string
): Promise<SessionQuestionScoreModel> {
  const correctOptions = question.Options?.filter(opt => opt.Correct) ?? []

  if (correctOptions.length === 0) {
    // no correct options, this should never happen
    log('error', 'Failed to grade question, no correct answers set on question', { questionId: question.Id, sessionId, success: false, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Pending: true,
      PartiallyCorrect: false,
      Correct: false
    }))
  }

  if (userResponses.length === 0) {
    // no response
    log('info', 'Cannot grade question, no responses. Setting score to 0.', { questionId: question.Id, sessionId, success: true, eventType: EventType.question_grade })
    return await saveQuestionScore(new SessionQuestionScoreModel({
      QuestionId: question.Id,
      QuestionVersion: question.Version,
      SessionId: sessionId,
      Score: 0,
      Pending: false,
      PartiallyCorrect: false,
      Correct: false
    }))
    
  }

  const response = userResponses[0]
  const correct = question.Options.some(option => option.Id === response.fields.OptionId && option.Correct)
  
  // set if the response is correct
  response.fields.Correct = correct
  // save the response
  await createQuestionResponse(response)

  // save the question score
  log('info', 'Successfully graded question.', { questionId: question.Id, sessionId, correct, success: true, eventType: EventType.question_grade })
  return await saveQuestionScore(new SessionQuestionScoreModel({
    QuestionId: question.Id,
    QuestionVersion: question.Version,
    SessionId: sessionId,
    Correct: correct,
    Score: correct ? question.Points ?? 1 : 0,
    Pending: false,
    PartiallyCorrect: false
  }))
}
