import { Table } from '@lcs/mssql-utility'
import { QuestionResponse, QuestionResponseFields, QuestionResponsesTableName } from '@tess-f/sql-tables/dist/evaluations/question-response.js'

export default class QuestionResponseModel extends Table<QuestionResponse, QuestionResponse> {
  fields: QuestionResponse

  constructor (fields?: QuestionResponse, record?: QuestionResponse) {
    super(
      QuestionResponsesTableName,
      [
        QuestionResponseFields.SessionId,
        QuestionResponseFields.QuestionId,
        QuestionResponseFields.QuestionVersion
      ]
    )

    this.fields = fields ?? {}
    if (record) this.importFromDatabase(record)
  }

  importFromDatabase (record: QuestionResponse): void {
    this.fields = {
      Id: record.Id,
      SessionId: record.SessionId,
      QuestionId: record.QuestionId,
      QuestionVersion: record.QuestionVersion,
      OptionId: record.OptionId,
      OptionVersion: record.OptionVersion,
      TargetOptionId: record.TargetOptionId,
      TargetOptionVersion: record.TargetOptionVersion,
      Notes: record.Notes,
      ResponseText: record.ResponseText,
      Correct: record.Correct,
      Score: record.Score,
      Duration: record.Duration,
      OrderId: record.OrderId
    }
  }

  exportJsonToDatabase (): QuestionResponse {
    return {
      Id: this.fields.Id,
      SessionId: this.fields.SessionId,
      QuestionId: this.fields.QuestionId,
      QuestionVersion: this.fields.QuestionVersion,
      OptionId: this.fields.OptionId,
      OptionVersion: this.fields.OptionVersion,
      TargetOptionId: this.fields.TargetOptionId,
      TargetOptionVersion: this.fields.TargetOptionVersion,
      Notes: this.fields.Notes,
      ResponseText: this.fields.ResponseText,
      Correct: this.fields.Correct,
      Score: this.fields.Score,
      Duration: this.fields.Duration,
      OrderId: this.fields.OrderId
    }
  }
}
