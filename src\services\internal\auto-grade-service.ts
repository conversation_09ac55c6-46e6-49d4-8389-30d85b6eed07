import logger from '@lcs/logger'
import { QuestionTypes } from '@tess-f/sql-tables/dist/evaluations/question-type.js'
import { EventType } from '@tess-f/shared-config/dist/tess-config/logger.js'
import gradeFillInTheBlank from './graders/grade-fill-in-blank.js'
import gradeOrdering from './graders/grade-ordering.js'
import gradeMatching from './graders/grade-matching.js'
import gradeDefaultQuestionType from './graders/grade-default.js'
import graderHotSpots from './graders/grade-hot-spots.js'
import QuestionResponseModel from '../../models/question-response.model.js'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import setQuestionPoints from './set-question-points.service.js'
import saveQuestionScore from '../mssql/session-question-scores/save-question-score.service.js'
import SessionQuestionScoreModel from '../../models/session-question-score.model.js'

const log = logger.create('Service.auto-grade')

const gradeFunctions = new Map<QuestionTypes, (question: QuestionWithOptions, userResponses: QuestionResponseModel[], sessionId: string) => Promise<SessionQuestionScoreModel>>([
  [QuestionTypes.TrueFalse, gradeDefaultQuestionType],
  [QuestionTypes.CheckAll, gradeDefaultQuestionType],
  [QuestionTypes.MultipleChoice, gradeDefaultQuestionType],
  [QuestionTypes.Matching, gradeMatching],
  [QuestionTypes.Ordering, gradeOrdering],
  [QuestionTypes.FillInTheBlank, gradeFillInTheBlank],
  [QuestionTypes.HotSpot, graderHotSpots]
])

/**
 * Grade a single question and its responses
 *
 * @param question The question with options and points already set
 * @param responses The user's responses for this question (without IDs)
 * @param evaluationId Optional evaluation ID for point overrides
 * @param evaluationVersion Optional evaluation version for point overrides
 * @returns The grading result
 */
export default async function gradeQuestion(
  question: QuestionWithOptions,
  responses: QuestionResponseModel[],
  sessionId: string,
  evaluationId?: string,
  evaluationVersion?: number
): Promise<void> {
  try {
    log('info', 'Grading question', { 
      questionId: question.Id, 
      questionVersion: question.Version, 
      responseCount: responses.length, 
      eventType: EventType.question_grade 
    })
    
    await setQuestionPoints(question, evaluationId, evaluationVersion)

    if (!responses || responses.length === 0) {
      // Handle no responses - automatic 0, no pending
      await saveQuestionScore(new SessionQuestionScoreModel({
        QuestionId: question.Id,
        QuestionVersion: question.Version,
        SessionId: sessionId,
        Score: 0,
        Pending: false,
        PartiallyCorrect: false,
        Correct: false
      }))
    }
    
    const questionTypeId = question.QuestionTypeId as QuestionTypes
    const gradeFunction = gradeFunctions.get(questionTypeId)

    if (gradeFunction) {
      gradeFunction(question, responses, sessionId)
      log('info', 'Question graded successfully', {
        questionId: question.Id,
        questionVersion: question.Version,
        eventType: EventType.question_grade
      })
    } else {
      log('warn', 'Cannot grade question: unknown type. Setting pending score to 0.', {
        questionId: question.Id,
        questionTypeId,
        eventType: EventType.question_grade
      })
      await saveQuestionScore(new SessionQuestionScoreModel({
        QuestionId: question.Id,
        QuestionVersion: question.Version,
        Score: 0,
        Pending: true,
        PartiallyCorrect: false,
        Correct: false,
        SessionId: sessionId
      }))
    }
  } catch (error) {
    log('error', 'Error grading question', { 
      questionId: question.Id, 
      questionVersion: question.Version, 
      error, 
      eventType: EventType.question_grade 
    })
    throw error
  }
}
