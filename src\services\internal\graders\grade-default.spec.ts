import { describe, it, before } from 'mocha'
import { expect } from 'chai'
import logger from '@lcs/logger'
import { QuestionTypes } from '@tess-f/sql-tables/dist/evaluations/question-type.js'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'
import QuestionResponseModel from '../../../models/question-response.model.js'
import esmock from 'esmock'
import Sinon from 'sinon'
import SessionQuestionScoreModel from '../../../models/session-question-score.model.js'

describe('grade-default', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  const createMockQuestion = (questionType: QuestionTypes, options: Array<{ Id: string; Version: number; Correct: boolean }> = []): QuestionWithOptions => ({
    Id: 'test-question-id',
    Version: 1,
    QuestionTypeId: questionType,
    Options: options,
    Points: 10,
    EnablePartialCredit: true
  })

  const createResponse = (optionId: string): QuestionResponseModel => (new QuestionResponseModel({
    Id: `response-${optionId}`,
    OptionId: optionId,
    OptionVersion: 1,
    ResponseText: ''
  }))

  describe('Multiple Choice Questions', () => {
    const mcOptions = [
      { Id: 'option-1', Version: 1, Correct: true },
      { Id: 'option-2', Version: 1, Correct: false },
      { Id: 'option-3', Version: 1, Correct: false }
    ]

    it('should grade correct multiple choice answer as correct', async () => {
      const question = createMockQuestion(QuestionTypes.MultipleChoice, mcOptions)
      const responses = [createResponse('option-1')]
      const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
      const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
        './grade-default.js', {
          '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
          '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
        }
      )

      await mock(question, responses, '10')

      expect(saveQuestionMock.called).to.equal(true)
      Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Correct: true, QuestionId: question.Id, QuestionVersion: question.Version, SessionId: '10', Score: question.Points ?? 1, Pending: false, PartiallyCorrect: false }))
    })

    it('should grade incorrect multiple choice answer as incorrect', async () => {
      const question = createMockQuestion(QuestionTypes.MultipleChoice, mcOptions)
      const responses = [createResponse('option-2')]
      const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
      const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
        './grade-default.js', {
          '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
          '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
        }
      )

      await mock(question, responses, '10')

      expect(saveQuestionMock.called).to.equal(true)
      Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Correct: false, QuestionId: question.Id, QuestionVersion: question.Version, SessionId: '10', Score: 0, Pending: false, PartiallyCorrect: false }))
    })
  })

  describe('True/False Questions', () => {
    const tfOptions = [
      { Id: 'true-option', Version: 1, Correct: true },
      { Id: 'false-option', Version: 1, Correct: false }
    ]

    it('should grade correct true/false answer as correct', async () => {
      const question = createMockQuestion(QuestionTypes.TrueFalse, tfOptions)
      const responses = [createResponse('true-option')]
      
      const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
      const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
        './grade-default.js', {
          '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
          '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
        }
      )

      await mock(question, responses, '5')

      expect(saveQuestionMock.called).to.equal(true)
      Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Correct: true, QuestionId: question.Id, QuestionVersion: question.Version, SessionId: '5', Score: question.Points ?? 1, PartiallyCorrect: false, Pending: false }))
    })
  })

  describe('Check All Questions', () => {
    const checkAllOptions = [
      { Id: 'option-1', Version: 1, Correct: true },
      { Id: 'option-2', Version: 1, Correct: true },
      { Id: 'option-3', Version: 1, Correct: false },
      { Id: 'option-4', Version: 1, Correct: false }
    ]

    it('should award full points for all correct selections', async () => {
      const question = createMockQuestion(QuestionTypes.CheckAll, checkAllOptions)
      const responses = [
        createResponse('option-1'),
        createResponse('option-2')
      ]
      
      const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
      const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
        './grade-default.js', {
          '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
          '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
        }
      )

      await mock(question, responses, '10')
      expect(saveQuestionMock.called).to.equal(true)
      Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Correct: true, Score: 10 }))
    })

    it('should award partial credit for some correct selections', async () => {
      const question = createMockQuestion(QuestionTypes.CheckAll, checkAllOptions)
      const responses = [createResponse('option-1')] // Only 1 of 2 correct

      const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
      const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
        './grade-default.js', {
          '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
          '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
        }
      )
      await mock(question, responses, '10')
      expect(saveQuestionMock.called).to.equal(true)
      Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: 5, Correct: false, PartiallyCorrect: true, QuestionId: question.Id, QuestionVersion: question.Version, Pending: false }))
    })

    it('should handle incorrect selections with partial credit', async () => {
      const question = createMockQuestion(QuestionTypes.CheckAll, checkAllOptions)
      const responses = [
        createResponse('option-1'), // Correct
        createResponse('option-3')  // Incorrect
      ]
      const saveQuestionMock = Sinon.stub().returns(Promise.resolve(new SessionQuestionScoreModel({})))
      const mock = await esmock<(question: QuestionWithOptions, responses: QuestionResponseModel[], sessionId: string) => Promise<void>>(
        './grade-default.js', {
          '../../mssql/session-question-scores/save-question-score.service.js': saveQuestionMock,
          '../../mssql/question-response/create.service.js': Sinon.stub().returns(Promise.resolve(new QuestionResponseModel({})))
        }
      )

      await mock(question, responses, '10')
      expect(saveQuestionMock.called).to.equal(true)
      Sinon.assert.calledWithMatch(saveQuestionMock, new SessionQuestionScoreModel({ Score: 3, Correct: false, Pending: false, PartiallyCorrect: true, QuestionId: question.Id, QuestionVersion: question.Version }))
    })
  })
})

